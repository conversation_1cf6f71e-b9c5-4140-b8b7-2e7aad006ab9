#!/usr/bin/env node

/**
 * Environment Switcher
 *
 * This script helps you switch between local and remote Supabase environments.
 *
 * Usage:
 * node scripts/switch-env.js local
 * node scripts/switch-env.js remote
 * node scripts/switch-env.js status
 */

const fs = require('fs');
const path = require('path');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

const envPath = path.resolve('.env.local');

const localConfig = {
  NEXT_PUBLIC_SUPABASE_URL: 'http://127.0.0.1:54321',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
  SUPABASE_ENV: 'local'
};

const remoteConfig = {
  NEXT_PUBLIC_SUPABASE_URL: 'https://cfagwxqgbojsatddfhac.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNmYWd3eHFnYm9qc2F0ZGRmaGFjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3NDY4MzAsImV4cCI6MjA2MjMyMjgzMH0.NNB-iKQmhbq0F5ieT4VI_MRkC2O_-28NqFEQgKVmQ5o',
  SUPABASE_ENV: 'production'
};

function readEnvFile() {
  try {
    const content = fs.readFileSync(envPath, 'utf8');
    const env = {};

    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });

    return env;
  } catch (error) {
    console.error(colorize('❌ Error reading .env.local file:', 'red'), error.message);
    return {};
  }
}

function writeEnvFile(env) {
  try {
    const lines = [];

    // Add header comment
    lines.push('# Supabase Configuration');
    lines.push(`# Environment: ${env.SUPABASE_ENV}`);
    lines.push('');

    // Add Supabase config
    lines.push(`NEXT_PUBLIC_SUPABASE_URL=${env.NEXT_PUBLIC_SUPABASE_URL}`);
    lines.push(`NEXT_PUBLIC_SUPABASE_ANON_KEY=${env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`);
    lines.push('');

    // Add other existing config
    const existingEnv = readEnvFile();
    Object.keys(existingEnv).forEach(key => {
      if (!key.startsWith('NEXT_PUBLIC_SUPABASE_') && key !== 'SUPABASE_ENV') {
        lines.push(`${key}=${existingEnv[key]}`);
      }
    });

    // Add environment marker
    lines.push('');
    lines.push(`SUPABASE_ENV=${env.SUPABASE_ENV}`);

    fs.writeFileSync(envPath, lines.join('\n'));
    console.log(colorize('✅ Environment configuration updated', 'green'));
  } catch (error) {
    console.error(colorize('❌ Error writing .env.local file:', 'red'), error.message);
  }
}

function getCurrentEnv() {
  const env = readEnvFile();
  const isLocal = env.NEXT_PUBLIC_SUPABASE_URL?.includes('127.0.0.1') ||
                  env.SUPABASE_ENV === 'local';
  return isLocal ? 'local' : 'remote';
}

function showStatus() {
  const currentEnv = getCurrentEnv();
  const env = readEnvFile();

  console.log(colorize('\n📊 Current Supabase Configuration', 'bright'));
  console.log('─'.repeat(50));
  console.log(`Environment: ${colorize(currentEnv.toUpperCase(), currentEnv === 'local' ? 'yellow' : 'green')}`);
  console.log(`URL: ${env.NEXT_PUBLIC_SUPABASE_URL}`);
  console.log(`Key: ${env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...`);
  console.log('');
}

function switchToLocal() {
  console.log(colorize('🔄 Switching to LOCAL Supabase environment...', 'yellow'));
  writeEnvFile(localConfig);
  console.log(colorize('📍 Now using local Supabase instance', 'yellow'));
  console.log(colorize('💡 Make sure to run: npm run supabase:start', 'cyan'));
}

function switchToRemote() {
  console.log(colorize('🔄 Switching to REMOTE Supabase environment...', 'green'));
  writeEnvFile(remoteConfig);
  console.log(colorize('📍 Now using remote Supabase instance', 'green'));
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  console.log(colorize('\n🔧 Supabase Environment Switcher', 'bright'));

  switch (command) {
    case 'local':
      switchToLocal();
      break;
    case 'remote':
      switchToRemote();
      break;
    case 'status':
      showStatus();
      break;
    default:
      console.log(colorize('\n📖 Usage:', 'bright'));
      console.log('  node scripts/switch-env.js local   - Switch to local Supabase');
      console.log('  node scripts/switch-env.js remote  - Switch to remote Supabase');
      console.log('  node scripts/switch-env.js status  - Show current configuration');
      showStatus();
  }
}

main();
