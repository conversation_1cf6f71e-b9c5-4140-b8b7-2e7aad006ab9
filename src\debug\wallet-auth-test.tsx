'use client';

import { useWalletAuth } from '@/context/WalletAuthContext';
import { useAccount } from 'wagmi';
import { useSupabaseClient } from '@supabase/auth-helpers-react';

export default function WalletAuthTest() {
  const {
    user,
    session,
    loading,
    isWalletConnected,
    walletAddress,
    isAuthenticated,
    signInWithWallet,
    signOut,
    error,
    walletError,
    signingIn
  } = useWalletAuth();

  const { address, isConnected } = useAccount();
  const supabase = useSupabaseClient();

  const testDatabaseConnection = async () => {
    try {
      console.log('Testing database connection...');
      const { data, error } = await supabase
        .from('profiles')
        .select('count(*)')
        .limit(1);

      if (error) {
        console.error('Database connection error:', error);
      } else {
        console.log('Database connection successful:', data);
      }
    } catch (err) {
      console.error('Database test failed:', err);
    }
  };

  const testProfileQuery = async () => {
    if (!walletAddress) {
      console.log('No wallet address available');
      return;
    }

    try {
      console.log('Testing profile query for address:', walletAddress);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('wallet_address', walletAddress.toLowerCase())
        .single();

      console.log('Profile query result:', { data, error });
    } catch (err) {
      console.error('Profile query test failed:', err);
    }
  };

  const testProfileInsert = async () => {
    if (!walletAddress || !session?.user?.id) {
      console.log('No wallet address or session available');
      return;
    }

    try {
      console.log('Testing profile insert for user:', session.user.id);
      const { data, error } = await supabase
        .from('profiles')
        .insert({
          id: session.user.id,
          wallet_address: walletAddress.toLowerCase(),
          kyc_status: 'none',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      console.log('Profile insert result:', { data, error });
    } catch (err) {
      console.error('Profile insert test failed:', err);
    }
  };

  const testRLSPolicies = async () => {
    try {
      console.log('Testing RLS policies...');
      const { data, error } = await supabase
        .rpc('execute_sql', {
          sql: `
            SELECT
              schemaname,
              tablename,
              policyname,
              permissive,
              roles,
              cmd,
              qual,
              with_check
            FROM pg_policies
            WHERE tablename = 'profiles'
            ORDER BY policyname;
          `
        });

      console.log('RLS policies result:', { data, error });
    } catch (err) {
      console.error('RLS policies test failed:', err);
    }
  };

  const testSessionQuery = async () => {
    if (!session?.user?.id) {
      console.log('No session available');
      return;
    }

    try {
      console.log('Testing session-based profile query for user ID:', session.user.id);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      console.log('Session profile query result:', { data, error });
    } catch (err) {
      console.error('Session profile query test failed:', err);
    }
  };

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Wallet Authentication Debug</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Wallet State */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Wallet State</h2>
          <div className="space-y-2 text-sm">
            <div>Connected (wagmi): {isConnected ? 'Yes' : 'No'}</div>
            <div>Connected (context): {isWalletConnected ? 'Yes' : 'No'}</div>
            <div>Address (wagmi): {address || 'None'}</div>
            <div>Address (context): {walletAddress || 'None'}</div>
          </div>
        </div>

        {/* Auth State */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Auth State</h2>
          <div className="space-y-2 text-sm">
            <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
            <div>Loading: {loading ? 'Yes' : 'No'}</div>
            <div>Signing In: {signingIn ? 'Yes' : 'No'}</div>
            <div>Session ID: {session?.user?.id || 'None'}</div>
            <div>User ID: {user?.id || 'None'}</div>
          </div>
        </div>

        {/* Errors */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Errors</h2>
          <div className="space-y-2 text-sm">
            <div>General Error: {error || 'None'}</div>
            <div>Wallet Error: {walletError?.message || 'None'}</div>
            {walletError && (
              <div className="text-xs text-gray-400">
                Type: {walletError.type}<br/>
                Details: {walletError.details}
              </div>
            )}
          </div>
        </div>

        {/* User Profile */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">User Profile</h2>
          <div className="space-y-2 text-sm">
            {user ? (
              <>
                <div>ID: {user.id}</div>
                <div>Wallet: {user.wallet_address}</div>
                <div>KYC: {user.kyc_status}</div>
                <div>Role: {user.role || 'None'}</div>
                <div>Name: {user.full_name || 'None'}</div>
              </>
            ) : (
              <div>No user profile loaded</div>
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 space-y-4">
        <div className="flex flex-wrap gap-3">
          <button
            onClick={signInWithWallet}
            disabled={!isWalletConnected || signingIn}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded"
          >
            {signingIn ? 'Signing In...' : 'Sign In with Wallet'}
          </button>

          <button
            onClick={signOut}
            disabled={!isAuthenticated}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 rounded"
          >
            Sign Out
          </button>
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={testDatabaseConnection}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded"
          >
            Test Database Connection
          </button>

          <button
            onClick={testProfileQuery}
            disabled={!walletAddress}
            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded"
          >
            Test Profile Query
          </button>

          <button
            onClick={testSessionQuery}
            disabled={!session}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded"
          >
            Test Session Query
          </button>

          <button
            onClick={testProfileInsert}
            disabled={!walletAddress || !session}
            className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 rounded"
          >
            Test Profile Insert
          </button>

          <button
            onClick={testRLSPolicies}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded"
          >
            Test RLS Policies
          </button>
        </div>
      </div>

      {/* SQL Fix Instructions */}
      <div className="mt-6 p-4 bg-red-900/30 border border-red-700 rounded-lg">
        <h3 className="text-lg font-semibold mb-2 text-red-300">Database Fix Required</h3>
        <p className="text-sm text-gray-300 mb-3">
          If authentication is failing, you likely need to run this SQL in your Supabase SQL Editor:
        </p>
        <pre className="bg-gray-800 p-3 rounded text-xs overflow-x-auto">
{`-- Fix missing INSERT policy for profiles table
DROP POLICY IF EXISTS insert_own_profile ON profiles;
CREATE POLICY insert_own_profile ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);`}
        </pre>
        <p className="text-xs text-gray-400 mt-2">
          This allows users to create their own profile during wallet authentication.
        </p>
      </div>

      {/* Console Log Instructions */}
      <div className="mt-6 p-4 bg-gray-800 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Debug Instructions</h3>
        <p className="text-sm text-gray-300">
          Open your browser's developer console (F12) to see detailed logs during the authentication process.
          All wallet authentication steps are logged with the prefix "WalletAuth:".
        </p>
      </div>
    </div>
  );
}
