'use client';

import { useWalletAuth } from '@/context/WalletAuthContext';
import { useConnectModal } from '@rainbow-me/rainbowkit';
import { Wallet, Shield, User, ArrowRight, CheckCircle, Loader2 } from 'lucide-react';
import Button from '@/components/ui/Button';

export default function WalletOnboardingFlow() {
  const { openConnectModal } = useConnectModal();
  const {
    isWalletConnected,
    isAuthenticated,
    signInWithWallet,
    signingIn,
    loading,
    isProfileComplete,
    onboardingStep
  } = useWalletAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 text-indigo-500 animate-spin" />
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 text-center">
      <h1 className="text-4xl font-bold text-white mb-4">Welcome to BrickChain</h1>
      <p className="text-xl text-gray-300 mb-12">Get started in 3 simple steps</p>

      {/* Progress */}
      <div className="flex justify-center mb-12">
        {['connect', 'authenticate', 'profile'].map((step, i) => (
          <div key={step} className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              onboardingStep === step ? 'bg-indigo-600 text-white' :
              ['connect', 'authenticate', 'profile'].indexOf(onboardingStep) > i ? 'bg-green-600 text-white' :
              'bg-gray-700 text-gray-400'
            }`}>
              {['connect', 'authenticate', 'profile'].indexOf(onboardingStep) > i ?
                <CheckCircle size={20} /> : i + 1}
            </div>
            {i < 2 && <div className="w-16 h-0.5 bg-gray-600 mx-4" />}
          </div>
        ))}
      </div>

      {/* Current Step */}
      <div className="bg-gray-800/50 rounded-xl p-8 border border-gray-700">
        {onboardingStep === 'connect' && (
          <>
            <Wallet size={48} className="text-indigo-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">Connect Your Wallet</h2>
            <p className="text-gray-300 mb-8">Connect your Web3 wallet to get started</p>
            <Button onClick={openConnectModal} variant="primary" size="lg" icon={<Wallet size={20} />}>
              Connect Wallet
            </Button>
          </>
        )}

        {onboardingStep === 'authenticate' && (
          <>
            <Shield size={48} className="text-indigo-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">Verify Identity</h2>
            <p className="text-gray-300 mb-8">Sign a message to create your account</p>
            <Button
              onClick={signInWithWallet}
              variant="primary"
              size="lg"
              loading={signingIn}
              icon={<Shield size={20} />}
            >
              Sign Message
            </Button>
          </>
        )}

        {onboardingStep === 'profile' && (
          <>
            <User size={48} className="text-indigo-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">Complete Profile</h2>
            <p className="text-gray-300 mb-8">Add your name and email to finish setup</p>
            <Button
              onClick={() => window.location.href = '/complete-profile'}
              variant="primary"
              size="lg"
              icon={<ArrowRight size={20} />}
            >
              Complete Profile
            </Button>
          </>
        )}

        {onboardingStep === 'complete' && (
          <>
            <CheckCircle size={48} className="text-green-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">Welcome!</h2>
            <p className="text-gray-300 mb-8">Your account is ready</p>
            <Button
              onClick={() => window.location.href = '/dashboard'}
              variant="primary"
              size="lg"
              icon={<ArrowRight size={20} />}
            >
              Go to Dashboard
            </Button>
          </>
        )}
      </div>
    </div>
  );
}
