/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // Enable image optimization
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com', 'fonts.gstatic.com'],
    formats: ['image/avif', 'image/webp'],
  },

  // Enable experimental features for better performance
  experimental: {
    // Disable optimized CSS to avoid critters issues
    optimizeCss: false,

    // Enable memory cache for faster builds
    memoryBasedWorkersCount: true,

    // Enable optimized bundle splitting
    optimizePackageImports: ['lucide-react'],
  },

  // Server external packages (moved from experimental.serverComponentsExternalPackages)
  serverExternalPackages: [],

  // Configure Turbopack for faster development
  turbopack: {
    // Disable Turbopack for now to avoid font loading issues
  },

  // Configure compiler options
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Enable static page generation for faster navigation
  // Note: 'hybrid' is not a valid value, using 'standalone' instead
  output: 'standalone',

  // Configure page prefetching
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 60 * 60 * 1000, // 1 hour
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 5,
  },

  // Configure headers for better caching
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // Configure webpack to handle font loading issues
  webpack(config) {
    return config;
  },
};

module.exports = nextConfig;
