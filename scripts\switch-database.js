#!/usr/bin/env node

/**
 * Database Switcher
 * 
 * This script helps you switch between different Supabase databases.
 * 
 * Usage:
 * node scripts/switch-database.js --setup
 * node scripts/switch-database.js --use production
 * node scripts/switch-database.js --use development
 * node scripts/switch-database.js --list
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

const envPath = path.resolve('.env.local');
const configPath = path.resolve('supabase-databases.json');

// Default databases configuration
const defaultDatabases = {
  production: {
    name: "Production Database",
    url: "https://cfagwxqgbojsatddfhac.supabase.co",
    anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNmYWd3eHFnYm9qc2F0ZGRmaGFjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3NDY4MzAsImV4cCI6MjA2MjMyMjgzMH0.NNB-iKQmhbq0F5ieT4VI_MRkC2O_-28NqFEQgKVmQ5o",
    description: "Original production database"
  },
  local: {
    name: "Local Development",
    url: "http://127.0.0.1:54321",
    anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxvY2FsaG9zdCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQ1MTkyODI0LCJleHAiOjE5NjA3Njg4MjR9.M9jrxyvPLkUxWgOYSf5dNdJ8v_eWrqwgNocOhKoYaKg",
    description: "Local Supabase instance"
  }
};

function loadDatabases() {
  try {
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
    return defaultDatabases;
  } catch (error) {
    console.error(colorize('❌ Error loading database config:', 'red'), error.message);
    return defaultDatabases;
  }
}

function saveDatabases(databases) {
  try {
    fs.writeFileSync(configPath, JSON.stringify(databases, null, 2));
    console.log(colorize('✅ Database configuration saved', 'green'));
  } catch (error) {
    console.error(colorize('❌ Error saving database config:', 'red'), error.message);
  }
}

function readEnvFile() {
  try {
    const content = fs.readFileSync(envPath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  } catch (error) {
    console.error(colorize('❌ Error reading .env.local file:', 'red'), error.message);
    return {};
  }
}

function writeEnvFile(env, databaseKey, database) {
  try {
    const lines = [];
    
    // Add header comment
    lines.push('# Supabase Configuration');
    lines.push(`# Current Database: ${database.name} (${databaseKey})`);
    lines.push('');
    
    // Add Supabase config
    lines.push(`NEXT_PUBLIC_SUPABASE_URL=${database.url}`);
    lines.push(`NEXT_PUBLIC_SUPABASE_ANON_KEY=${database.anon_key}`);
    lines.push('');
    
    // Add other existing config
    Object.keys(env).forEach(key => {
      if (!key.startsWith('NEXT_PUBLIC_SUPABASE_') && key !== 'SUPABASE_ENV' && key !== 'CURRENT_DATABASE') {
        lines.push(`${key}=${env[key]}`);
      }
    });
    
    // Add environment markers
    lines.push('');
    lines.push(`SUPABASE_ENV=${databaseKey === 'local' ? 'local' : 'production'}`);
    lines.push(`CURRENT_DATABASE=${databaseKey}`);
    
    fs.writeFileSync(envPath, lines.join('\n'));
    console.log(colorize('✅ Environment configuration updated', 'green'));
  } catch (error) {
    console.error(colorize('❌ Error writing .env.local file:', 'red'), error.message);
  }
}

function getCurrentDatabase() {
  const env = readEnvFile();
  return env.CURRENT_DATABASE || 'production';
}

function listDatabases() {
  const databases = loadDatabases();
  const current = getCurrentDatabase();
  
  console.log(colorize('\n📊 Available Databases', 'bright'));
  console.log('─'.repeat(60));
  
  Object.keys(databases).forEach(key => {
    const db = databases[key];
    const isCurrent = key === current;
    const marker = isCurrent ? colorize('→', 'green') : ' ';
    const name = isCurrent ? colorize(db.name, 'green') : db.name;
    
    console.log(`${marker} ${colorize(key, 'cyan')}: ${name}`);
    console.log(`   ${db.description}`);
    console.log(`   URL: ${db.url}`);
    console.log('');
  });
}

function switchDatabase(databaseKey) {
  const databases = loadDatabases();
  
  if (!databases[databaseKey]) {
    console.error(colorize(`❌ Database '${databaseKey}' not found`, 'red'));
    console.log(colorize('Available databases:', 'yellow'));
    Object.keys(databases).forEach(key => {
      console.log(`  - ${key}`);
    });
    return;
  }
  
  const database = databases[databaseKey];
  const env = readEnvFile();
  
  console.log(colorize(`🔄 Switching to ${database.name}...`, 'cyan'));
  writeEnvFile(env, databaseKey, database);
  console.log(colorize(`📍 Now using: ${database.name}`, 'green'));
}

async function setupNewDatabase() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const question = (prompt) => new Promise(resolve => rl.question(prompt, resolve));
  
  console.log(colorize('\n🔧 Setup New Database', 'bright'));
  console.log('─'.repeat(40));
  
  try {
    const key = await question('Database key (e.g., "development"): ');
    const name = await question('Database name (e.g., "Development Database"): ');
    const url = await question('Supabase URL: ');
    const anon_key = await question('Anon Key: ');
    const description = await question('Description (optional): ');
    
    const databases = loadDatabases();
    databases[key] = {
      name,
      url,
      anon_key,
      description: description || `${name} database`
    };
    
    saveDatabases(databases);
    console.log(colorize(`✅ Database '${key}' added successfully!`, 'green'));
    console.log(colorize(`💡 Use: node scripts/switch-database.js --use ${key}`, 'cyan'));
    
  } catch (error) {
    console.error(colorize('❌ Setup cancelled:', 'red'), error.message);
  } finally {
    rl.close();
  }
}

function showHelp() {
  console.log(colorize('\n🔧 Database Switcher', 'bright'));
  console.log('─'.repeat(40));
  console.log('Usage:');
  console.log('  node scripts/switch-database.js --setup           Setup new database');
  console.log('  node scripts/switch-database.js --use <key>       Switch to database');
  console.log('  node scripts/switch-database.js --list           List all databases');
  console.log('  node scripts/switch-database.js --current        Show current database');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/switch-database.js --use production');
  console.log('  node scripts/switch-database.js --use development');
  console.log('  node scripts/switch-database.js --use local');
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--setup')) {
    setupNewDatabase();
  } else if (args.includes('--list')) {
    listDatabases();
  } else if (args.includes('--current')) {
    const current = getCurrentDatabase();
    const databases = loadDatabases();
    const db = databases[current];
    console.log(colorize(`\n📍 Current Database: ${db?.name || current}`, 'green'));
    if (db) {
      console.log(`   Key: ${current}`);
      console.log(`   URL: ${db.url}`);
    }
  } else if (args.includes('--use')) {
    const keyIndex = args.indexOf('--use') + 1;
    if (keyIndex < args.length) {
      switchDatabase(args[keyIndex]);
    } else {
      console.error(colorize('❌ Please specify a database key', 'red'));
      showHelp();
    }
  } else {
    showHelp();
  }
}

main();
