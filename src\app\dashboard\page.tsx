'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useUser } from '../../context/UserContext';
import { Building, Briefcase, Wallet, ArrowRight, Loader2 } from 'lucide-react';
import Link from 'next/link';
import WalletAuthButton from '@/components/WalletAuthButton';

export default function Dashboard() {
  const router = useRouter();
  const { user: walletUser, isAuthenticated, loading: walletLoading, isProfileComplete, kycStatus } = useWalletAuth();
  const { user: legacyUser, loading: legacyLoading } = useUser();

  const [showKycBanner, setShowKycBanner] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine which user to use
  const user = walletUser || legacyUser;
  const loading = walletLoading || legacyLoading;

  // Check if KYC banner should be shown
  useEffect(() => {
    if (isAuthenticated && walletUser && kycStatus === 'none') {
      setShowKycBanner(true);
    }
  }, [isAuthenticated, walletUser, kycStatus]);

  // Redirect to profile completion if needed
  useEffect(() => {
    if (isAuthenticated && walletUser && !isProfileComplete) {
      router.push('/complete-profile');
    }
  }, [isAuthenticated, walletUser, isProfileComplete, router]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [loading, user, router]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-950 to-indigo-950">
        <div className="bg-red-900/40 border border-red-800 rounded-lg p-6 max-w-md">
          <h2 className="text-xl font-bold text-white mb-4">Dashboard Error</h2>
          <p className="text-red-300">{error}</p>
          <div className="mt-6 flex space-x-4">
            <button
              onClick={() => router.push('/')}
              className="px-4 py-2 bg-gray-600 rounded text-white"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-950 to-indigo-950">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mb-4" />
          <p className="text-white text-lg">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome header */}
        <div className="mb-10">
          <h1 className="text-3xl font-bold text-white mb-2">Welcome, {user.full_name || 'User'}</h1>
          <p className="text-gray-400">
            Your unified dashboard for all real estate activities
          </p>
        </div>

        {/* KYC Banner */}
        {showKycBanner && (
          <div className="bg-amber-900/30 border border-amber-800 rounded-lg p-4 mb-8">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-amber-300">KYC Verification Required</h3>
                <div className="mt-2 text-sm text-amber-200">
                  <p>
                    To invest in properties or list your own, you need to complete KYC verification.
                  </p>
                </div>
                <div className="mt-4">
                  <div className="-mx-2 -my-1.5 flex">
                    <button
                      type="button"
                      className="rounded-md bg-amber-900 px-3 py-1.5 text-sm font-medium text-amber-100 hover:bg-amber-800 focus:outline-none focus:ring-2 focus:ring-amber-600"
                      onClick={() => router.push('/kyc-verification')}
                    >
                      Complete KYC
                    </button>
                    <button
                      type="button"
                      className="ml-3 rounded-md bg-amber-900/20 px-3 py-1.5 text-sm font-medium text-amber-100 hover:bg-amber-900/30 focus:outline-none focus:ring-2 focus:ring-amber-600"
                      onClick={() => setShowKycBanner(false)}
                    >
                      Dismiss
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Wallet Status */}
        <div className="bg-indigo-900/30 border border-indigo-800/30 rounded-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="w-12 h-12 bg-indigo-600/30 rounded-full flex items-center justify-center mr-4">
                <Wallet className="h-6 w-6 text-indigo-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Wallet Connection</h2>
                <p className="text-gray-400">
                  {isAuthenticated
                    ? 'Your wallet is connected and authenticated'
                    : 'Connect your wallet to access all features'}
                </p>
              </div>
            </div>
            <WalletAuthButton />
          </div>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {/* Investment Opportunities */}
          <div className="bg-gradient-to-br from-indigo-900/20 to-purple-900/20 rounded-lg border border-indigo-800/30 p-6 hover:shadow-lg hover:shadow-indigo-900/20 transition-all">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
              <Building className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Investment Opportunities</h3>
            <p className="text-gray-400 mb-6">
              Explore available properties for investment from around the world.
            </p>
            <Link
              href="/marketplace"
              className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors"
            >
              <span>Browse Properties</span>
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>

          {/* My Portfolio */}
          <div className="bg-gradient-to-br from-indigo-900/20 to-purple-900/20 rounded-lg border border-indigo-800/30 p-6 hover:shadow-lg hover:shadow-indigo-900/20 transition-all">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
              <Briefcase className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">My Portfolio</h3>
            <p className="text-gray-400 mb-6">
              Track your investments, rental income, and property appreciation.
            </p>
            <Link
              href="/dashboard/portfolio"
              className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors"
            >
              <span>View Portfolio</span>
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>

          {/* Property Management */}
          <div className="bg-gradient-to-br from-indigo-900/20 to-purple-900/20 rounded-lg border border-indigo-800/30 p-6 hover:shadow-lg hover:shadow-indigo-900/20 transition-all">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
              <Building className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Property Management</h3>
            <p className="text-gray-400 mb-6">
              List and manage your properties on the BrickChain platform.
            </p>
            <Link
              href="/dashboard/properties"
              className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors"
            >
              <span>Manage Properties</span>
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}