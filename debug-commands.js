// MetaMask Connection Debug Commands
// Copy and paste these into Chrome DevTools Console

// 1. Check if MetaMask is available
console.log('MetaMask Detection:', {
  hasEthereum: !!window.ethereum,
  isMetaMask: window.ethereum?.isMetaMask,
  providers: window.ethereum?.providers?.length || 0,
  selectedAddress: window.ethereum?.selectedAddress
});

// 2. Test direct MetaMask connection
async function testMetaMaskConnection() {
  try {
    if (!window.ethereum) {
      throw new Error('MetaMask not detected');
    }
    
    const accounts = await window.ethereum.request({ 
      method: 'eth_requestAccounts' 
    });
    
    console.log('✅ MetaMask connected:', accounts[0]);
    return accounts[0];
  } catch (error) {
    console.error('❌ MetaMask connection failed:', error);
    throw error;
  }
}

// 3. Check current network
async function checkNetwork() {
  try {
    const chainId = await window.ethereum.request({ 
      method: 'eth_chainId' 
    });
    console.log('Current network:', parseInt(chainId, 16));
  } catch (error) {
    console.error('Network check failed:', error);
  }
}

// 4. Test wallet permissions
async function checkPermissions() {
  try {
    const permissions = await window.ethereum.request({
      method: 'wallet_getPermissions'
    });
    console.log('Wallet permissions:', permissions);
  } catch (error) {
    console.error('Permission check failed:', error);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🔍 Starting MetaMask diagnostics...');
  
  try {
    await testMetaMaskConnection();
    await checkNetwork();
    await checkPermissions();
    console.log('✅ All tests completed');
  } catch (error) {
    console.error('❌ Tests failed:', error);
  }
}

// Execute: runAllTests()
