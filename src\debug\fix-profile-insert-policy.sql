-- Fix missing INSERT policy for profiles table
-- Run this in your Supabase SQL Editor to fix the authentication issue

-- Add INSERT policy for users to create their own profile
DROP POLICY IF EXISTS insert_own_profile ON profiles;
CREATE POLICY insert_own_profile ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Also add a policy for admins to insert profiles (optional)
DROP POLICY IF EXISTS admin_insert_profiles ON profiles;
CREATE POLICY admin_insert_profiles ON profiles
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Verify the policies are in place
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'profiles'
ORDER BY policyname;
