'use client';

import { useState, useEffect } from 'react';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { Wallet, LinkIcon, ExternalLink, Loader2, ShieldCheck } from 'lucide-react';
import { getBlockExplorerUrl, formatAddress } from '@/utils/web3';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useToast } from '@/context/ToastContext';
import Button from '@/components/ui/Button';
import WalletErrorModal from './WalletErrorModal';

interface WalletAuthButtonProps {
  variant?: 'default' | 'minimal' | 'sidebar';
  showBalance?: boolean;
  showKycStatus?: boolean;
  size?: 'default' | 'sm' | 'lg';
}

/**
 * WalletAuthButton combines wallet connection with authentication.
 * It extends the functionality of ConnectWalletButton to include
 * authentication with the connected wallet.
 */
export default function WalletAuthButton({
  variant = 'default',
  showBalance = true,
  showKycStatus = false,
  size = 'default'
}: WalletAuthButtonProps) {
  const {
    isWalletConnected,
    walletAddress,
    isAuthenticated,
    signInWithWallet,
    loading,
    error,
    kycStatus,
    walletError,
    clearWalletError,
    signingIn
  } = useWalletAuth();

  const { success, error: showError } = useToast();

  // Handle wallet authentication
  const handleAuthenticate = async () => {
    if (!isWalletConnected) {
      showError('Wallet Not Connected', 'Please connect your wallet first.');
      return;
    }

    try {
      await signInWithWallet();
      success('Authentication Successful', 'You have been successfully authenticated with your wallet.');
    } catch (err) {
      showError('Authentication Failed', 'Failed to authenticate with wallet. Please try again.');
    }
  };

  // Handle wallet errors with toast notifications
  useEffect(() => {
    if (walletError) {
      showError('Wallet Error', walletError.message);
    }
  }, [walletError, showError]);

  return (
    <>
      <ConnectButton.Custom>
        {({
          account,
          chain,
          openAccountModal,
          openChainModal,
          openConnectModal,
          authenticationStatus,
          mounted,
        }) => {
        const connected = mounted &&
          account &&
          chain &&
          (!authenticationStatus || authenticationStatus === 'authenticated');

        if (variant === 'minimal') {
          return (
            <div className="flex items-center">
              {(() => {
                if (!connected) {
                  return (
                    <Button
                      onClick={openConnectModal}
                      variant="ghost"
                      size="sm"
                    >
                      Connect Wallet
                    </Button>
                  );
                }

                if (!isAuthenticated) {
                  return (
                    <Button
                      onClick={handleAuthenticate}
                      variant="ghost"
                      size="sm"
                      loading={signingIn}
                      loadingText="Signing In..."
                    >
                      Sign In
                    </Button>
                  );
                }

                return (
                  <Button
                    onClick={openAccountModal}
                    variant="ghost"
                    size="sm"
                  >
                    {account.displayName}
                  </Button>
                );
              })()}
            </div>
          );
        }

        if (variant === 'sidebar') {
          return (
            <div className="w-full">
              {(() => {
                if (!connected) {
                  return (
                    <Button
                      onClick={openConnectModal}
                      variant="primary"
                      size="lg"
                      fullWidth
                      rounded="full"
                      icon={<Wallet size={18} />}
                    >
                      Connect Wallet
                    </Button>
                  );
                }

                if (!isAuthenticated) {
                  return (
                    <Button
                      onClick={handleAuthenticate}
                      variant="primary"
                      size="lg"
                      fullWidth
                      rounded="full"
                      loading={signingIn}
                      loadingText="Signing In..."
                      icon={!signingIn ? <Wallet size={18} /> : undefined}
                    >
                      Sign In with Wallet
                    </Button>
                  );
                }

                return (
                  <div className="bg-gray-800/50 p-4 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 to-indigo-500 rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium">Connected</p>
                          <p className="text-xs text-gray-400">
                            {formatAddress(account.address)}
                          </p>
                        </div>
                      </div>
                      <button
                        className="text-xs text-red-400"
                        onClick={openAccountModal}
                      >
                        Disconnect
                      </button>
                    </div>

                    {showKycStatus && (
                      <div className="mt-2 border-t border-gray-700 pt-2">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-400">KYC Status</span>
                          <div className={`text-xs px-2 py-0.5 rounded-full flex items-center ${
                            kycStatus === 'verified'
                              ? 'bg-green-900/30 text-green-400'
                              : kycStatus === 'pending'
                                ? 'bg-amber-900/30 text-amber-400'
                                : 'bg-gray-800 text-gray-400'
                          }`}>
                            {kycStatus === 'verified' && (
                              <ShieldCheck size={10} className="mr-1" />
                            )}
                            {kycStatus === 'verified' ? 'Verified' : kycStatus === 'pending' ? 'Pending' : 'Required'}
                          </div>
                        </div>
                      </div>
                    )}
                    {showBalance && account.balanceFormatted && (
                      <div className="mt-3 border-t border-gray-700 pt-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Balance</span>
                          <span>{account.balanceFormatted}</span>
                        </div>
                      </div>
                    )}
                    <div className="mt-2 flex justify-between gap-2">
                      <button
                        onClick={openChainModal}
                        className="flex-1 text-xs px-2 py-1.5 bg-gray-700 hover:bg-gray-600 rounded text-center"
                      >
                        {chain.name}
                      </button>
                      <a
                        href={getBlockExplorerUrl(chain.id, account.address)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 text-xs px-2 py-1.5 bg-gray-700 hover:bg-gray-600 rounded flex items-center justify-center gap-1"
                      >
                        <ExternalLink size={12} />
                        Explorer
                      </a>
                    </div>
                  </div>
                );
              })()}
            </div>
          );
        }

        // Default variant
        return (
          <div className="flex items-center gap-3">
            {chain && connected && (
              <button
                onClick={openChainModal}
                className="hidden md:flex items-center gap-1 px-3 py-1.5 bg-indigo-900/50 border border-indigo-700/30 rounded-lg text-indigo-300 hover:bg-indigo-800/50 transition-colors"
                type="button"
              >
                {chain.hasIcon && (
                  <div className="w-5 h-5 rounded-full overflow-hidden">
                    {chain.iconUrl && (
                      <img
                        alt={chain.name ?? 'Chain icon'}
                        src={chain.iconUrl}
                        className="w-full h-full"
                      />
                    )}
                  </div>
                )}
                <span className="text-sm font-medium">{chain.name}</span>
              </button>
            )}

            {(() => {
              if (!connected) {
                return (
                  <Button
                    onClick={openConnectModal}
                    variant="primary"
                    size={size === 'lg' ? 'lg' : size === 'sm' ? 'sm' : 'md'}
                    icon={<Wallet size={size === 'lg' ? 20 : size === 'sm' ? 14 : 16} />}
                  >
                    Connect Wallet
                  </Button>
                );
              }

              if (!isAuthenticated) {
                return (
                  <Button
                    onClick={handleAuthenticate}
                    variant="primary"
                    size={size === 'lg' ? 'lg' : size === 'sm' ? 'sm' : 'md'}
                    loading={signingIn}
                    loadingText="Signing In..."
                    icon={!signingIn ? <Wallet size={size === 'lg' ? 20 : size === 'sm' ? 14 : 16} /> : undefined}
                  >
                    Sign In with Wallet
                  </Button>
                );
              }

              return (
                <div className="flex items-center gap-3">
                  {showKycStatus && (
                    <div className={`text-xs px-2 py-1 rounded-full flex items-center ${
                      kycStatus === 'verified'
                        ? 'bg-green-900/30 text-green-400 border border-green-800/50'
                        : kycStatus === 'pending'
                          ? 'bg-amber-900/30 text-amber-400 border border-amber-800/50'
                          : 'bg-gray-800 text-gray-400 border border-gray-700'
                    }`}>
                      {kycStatus === 'verified' && (
                        <ShieldCheck size={12} className="mr-1" />
                      )}
                      {kycStatus === 'verified' ? 'Verified' : kycStatus === 'pending' ? 'Pending' : 'KYC Required'}
                    </div>
                  )}

                  <button
                    onClick={openAccountModal}
                    className="px-3 py-1.5 bg-indigo-800/50 hover:bg-indigo-700/50 border border-indigo-500/30 text-indigo-300 rounded-lg flex items-center gap-2 transition-colors"
                    type="button"
                  >
                    <Wallet size={16} className="text-indigo-400" />
                    <span className="text-sm font-medium">
                      {account.displayName}
                    </span>
                    {showBalance && account.balanceFormatted && (
                      <span className="hidden md:inline text-sm font-medium">
                        ({account.balanceFormatted})
                      </span>
                    )}
                  </button>
                </div>
              );
            })()}
          </div>
        );
      }}
      </ConnectButton.Custom>

      {/* Error Modal */}
      <WalletErrorModal
        isOpen={!!walletError}
        onClose={clearWalletError}
        error={walletError}
        onRetry={handleAuthenticate}
      />
    </>
  );
}
