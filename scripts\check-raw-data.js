#!/usr/bin/env node

/**
 * Check Raw Data Script
 * 
 * This script checks data using service role to bypass all RLS policies
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration - Use service role for bypassing RLS
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function checkRawData() {
  console.log(colorize('\n📊 Checking Raw Database Data (Service Role)', 'bright'));
  console.log('─'.repeat(60));
  
  try {
    // Check users with service role
    console.log(colorize('\n👥 Users (Service Role Query):', 'blue'));
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*');
    
    if (usersError) {
      console.log(colorize(`❌ Error: ${usersError.message}`, 'red'));
    } else {
      console.log(colorize(`Found ${users?.length || 0} users`, 'cyan'));
      if (users && users.length > 0) {
        users.forEach(user => {
          console.log(`   • ${user.email} (${user.role}) - KYC: ${user.kyc_status}`);
          console.log(`     ID: ${user.id}`);
          console.log(`     Wallet: ${user.wallet_address}`);
        });
      }
    }
    
    // Check properties
    console.log(colorize('\n🏠 Properties (Service Role Query):', 'blue'));
    const { data: properties, error: propertiesError } = await supabase
      .from('properties')
      .select('*');
    
    if (propertiesError) {
      console.log(colorize(`❌ Error: ${propertiesError.message}`, 'red'));
    } else {
      console.log(colorize(`Found ${properties?.length || 0} properties`, 'cyan'));
      if (properties && properties.length > 0) {
        properties.forEach(property => {
          console.log(`   • ${property.name} - $${property.price?.toLocaleString()}`);
          console.log(`     ID: ${property.id}`);
          console.log(`     Owner: ${property.owner_id}`);
          console.log(`     Status: ${property.status}`);
        });
      }
    }
    
    // Check investments
    console.log(colorize('\n💰 Investments (Service Role Query):', 'blue'));
    const { data: investments, error: investmentsError } = await supabase
      .from('investments')
      .select('*');
    
    if (investmentsError) {
      console.log(colorize(`❌ Error: ${investmentsError.message}`, 'red'));
    } else {
      console.log(colorize(`Found ${investments?.length || 0} investments`, 'cyan'));
      if (investments && investments.length > 0) {
        investments.forEach(investment => {
          console.log(`   • $${investment.amount?.toLocaleString()} (${investment.shares} shares)`);
          console.log(`     Property: ${investment.property_id}`);
          console.log(`     Investor: ${investment.investor_id}`);
          console.log(`     Status: ${investment.status}`);
        });
      }
    }
    
    // Check table existence
    console.log(colorize('\n🔍 Table Information:', 'blue'));
    const { data: tables, error: tablesError } = await supabase.rpc('exec_sql', {
      sql: "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name"
    });
    
    if (tablesError) {
      console.log(colorize(`❌ Error: ${tablesError.message}`, 'red'));
    } else if (tables && tables.length > 0) {
      console.log('Available tables:');
      tables.forEach(table => {
        console.log(`   • ${table.result.table_name}`);
      });
    }
    
    // Check RLS status
    console.log(colorize('\n🔒 RLS Status:', 'blue'));
    const { data: rlsStatus, error: rlsError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          schemaname, 
          tablename, 
          rowsecurity 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        ORDER BY tablename
      `
    });
    
    if (rlsError) {
      console.log(colorize(`❌ Error: ${rlsError.message}`, 'red'));
    } else if (rlsStatus && rlsStatus.length > 0) {
      rlsStatus.forEach(table => {
        const status = table.result.rowsecurity ? 'ENABLED' : 'DISABLED';
        const color = table.result.rowsecurity ? 'green' : 'yellow';
        console.log(`   • ${table.result.tablename}: ${colorize(status, color)}`);
      });
    }
    
  } catch (error) {
    console.error(colorize('\n❌ Connection error:', 'red'), error.message);
  }
}

checkRawData();
