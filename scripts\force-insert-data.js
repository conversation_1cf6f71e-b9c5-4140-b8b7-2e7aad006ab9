#!/usr/bin/env node

/**
 * Force Insert Sample Data
 * 
 * This script uses raw SQL to bypass all constraints and insert sample data
 * directly into the database for local development.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration - Use service role for bypassing everything
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function executeSQL(sql, description) {
  console.log(colorize(`🔄 ${description}...`, 'cyan'));
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(colorize(`❌ Error: ${error.message}`, 'red'));
      return false;
    }
    
    console.log(colorize(`✅ ${description} completed`, 'green'));
    return true;
  } catch (error) {
    console.error(colorize(`❌ Unexpected error: ${error.message}`, 'red'));
    return false;
  }
}

async function main() {
  console.log(colorize('\n🚀 Force Inserting Sample Data', 'bright'));
  console.log(colorize('This bypasses all constraints for local development', 'yellow'));
  console.log('─'.repeat(60));
  
  try {
    // Step 1: Disable RLS temporarily
    await executeSQL('ALTER TABLE public.users DISABLE ROW LEVEL SECURITY', 'Disabling RLS for users');
    await executeSQL('ALTER TABLE public.properties DISABLE ROW LEVEL SECURITY', 'Disabling RLS for properties');
    await executeSQL('ALTER TABLE public.investments DISABLE ROW LEVEL SECURITY', 'Disabling RLS for investments');
    
    // Step 2: Clear existing data
    await executeSQL(`
      DELETE FROM public.investments WHERE investor_id IN (
        '550e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440003'
      )
    `, 'Clearing existing investments');
    
    await executeSQL(`
      DELETE FROM public.properties WHERE owner_id IN (
        '550e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440003'
      )
    `, 'Clearing existing properties');
    
    await executeSQL(`
      DELETE FROM public.users WHERE id IN (
        '550e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440003'
      )
    `, 'Clearing existing users');
    
    // Step 3: Insert users
    await executeSQL(`
      INSERT INTO public.users (id, email, wallet_address, role, kyc_status, created_at, updated_at) VALUES
      (
        '550e8400-e29b-41d4-a716-446655440001',
        '<EMAIL>',
        '******************************************',
        'investor',
        'verified',
        now(),
        now()
      ),
      (
        '550e8400-e29b-41d4-a716-446655440002',
        '<EMAIL>',
        '0x8ba1f109551bD432803012645Hac136c30C6756',
        'property_owner',
        'verified',
        now(),
        now()
      ),
      (
        '550e8400-e29b-41d4-a716-446655440003',
        '<EMAIL>',
        '******************************************',
        'admin',
        'verified',
        now(),
        now()
      )
    `, 'Inserting sample users');
    
    // Step 4: Insert properties
    await executeSQL(`
      INSERT INTO public.properties (id, name, description, price, location, property_type, bedrooms, bathrooms, square_feet, status, owner_id, images, metadata, created_at, updated_at) VALUES
      (
        '660e8400-e29b-41d4-a716-446655440001',
        'Luxury Downtown Apartment',
        'A stunning 2-bedroom apartment in the heart of downtown with panoramic city views.',
        500000,
        'Downtown, City Center',
        'apartment',
        2,
        2,
        1200,
        'active',
        '550e8400-e29b-41d4-a716-446655440002',
        ARRAY['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800'],
        '{"year_built": 2018, "parking_spaces": 1, "amenities": ["gym", "pool"]}'::jsonb,
        now(),
        now()
      ),
      (
        '660e8400-e29b-41d4-a716-446655440002',
        'Suburban Family Home',
        'Perfect family home with large backyard and excellent school district.',
        750000,
        'Suburban Heights',
        'house',
        4,
        3,
        2500,
        'active',
        '550e8400-e29b-41d4-a716-446655440002',
        ARRAY['https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800'],
        '{"year_built": 2010, "parking_spaces": 2, "amenities": ["garden", "garage"]}'::jsonb,
        now(),
        now()
      )
    `, 'Inserting sample properties');
    
    // Step 5: Insert investments
    await executeSQL(`
      INSERT INTO public.investments (id, property_id, investor_id, amount, shares, transaction_hash, status, created_at, updated_at) VALUES
      (
        '770e8400-e29b-41d4-a716-446655440001',
        '660e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440001',
        50000,
        10,
        '******************************************901234567890abcdef1234567890',
        'completed',
        now(),
        now()
      )
    `, 'Inserting sample investments');
    
    // Step 6: Re-enable RLS
    await executeSQL('ALTER TABLE public.users ENABLE ROW LEVEL SECURITY', 'Re-enabling RLS for users');
    await executeSQL('ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY', 'Re-enabling RLS for properties');
    await executeSQL('ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY', 'Re-enabling RLS for investments');
    
    console.log(colorize('\n🎉 Sample data inserted successfully!', 'green'));
    
    // Verify the data
    console.log(colorize('\n📊 Verifying data...', 'blue'));
    
    const { data: userCount } = await supabase.rpc('exec_sql', { 
      sql: 'SELECT COUNT(*) as count FROM public.users' 
    });
    const { data: propertyCount } = await supabase.rpc('exec_sql', { 
      sql: 'SELECT COUNT(*) as count FROM public.properties' 
    });
    const { data: investmentCount } = await supabase.rpc('exec_sql', { 
      sql: 'SELECT COUNT(*) as count FROM public.investments' 
    });
    
    console.log(colorize(`✅ Users: ${userCount?.[0]?.result?.count || 0}`, 'green'));
    console.log(colorize(`✅ Properties: ${propertyCount?.[0]?.result?.count || 0}`, 'green'));
    console.log(colorize(`✅ Investments: ${investmentCount?.[0]?.result?.count || 0}`, 'green'));
    
    console.log(colorize('\n🚀 Next Steps:', 'bright'));
    console.log('1. Check Supabase Studio: http://127.0.0.1:54323');
    console.log('2. View your Next.js app: http://localhost:3000');
    console.log('3. Test the marketplace and dashboard features');
    console.log('4. Run: npm run db:check');
    
  } catch (error) {
    console.error(colorize('\n❌ Script failed:', 'red'), error.message);
    
    // Make sure to re-enable RLS even if something fails
    await executeSQL('ALTER TABLE public.users ENABLE ROW LEVEL SECURITY', 'Re-enabling RLS for users');
    await executeSQL('ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY', 'Re-enabling RLS for properties');
    await executeSQL('ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY', 'Re-enabling RLS for investments');
    
    process.exit(1);
  }
}

main();
