'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAccount, useSignMessage, useDisconnect, useChainId } from 'wagmi';
import { SiweMessage } from 'siwe';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import type { SupabaseClient, Session } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { parseWalletError, type WalletError, retryWalletConnection } from '@/utils/walletErrorHandling';

export type UserProfile = {
  id: string;
  wallet_address: string;
  full_name: string | null;
  email: string | null;
  phone: string | null;
  kyc_status: 'none' | 'pending' | 'verified' | 'rejected';
  created_at: string;
  updated_at: string;
  profile_picture_url?: string;
};

type WalletAuthContextType = {
  // User state
  user: UserProfile | null;
  session: Session | null;
  loading: boolean;

  // Wallet state
  isWalletConnected: boolean;
  walletAddress: string | undefined;

  // Authentication state
  isAuthenticated: boolean;

  // Authentication actions
  signInWithWallet: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;

  // Profile completion status
  isProfileComplete: boolean;

  // KYC status
  kycStatus: 'none' | 'pending' | 'verified' | 'rejected';

  // Error state
  error: string | null;
  walletError: WalletError | null;
  clearWalletError: () => void;

  // Loading states
  connectingWallet: boolean;
  signingIn: boolean;
};

const WalletAuthContext = createContext<WalletAuthContextType>({
  user: null,
  session: null,
  loading: true,
  isWalletConnected: false,
  walletAddress: undefined,
  isAuthenticated: false,
  signInWithWallet: async () => {},
  signOut: async () => {},
  refreshUser: async () => {},
  isProfileComplete: false,
  kycStatus: 'none',
  error: null,
  walletError: null,
  clearWalletError: () => {},
  connectingWallet: false,
  signingIn: false,
});

export const useWalletAuth = () => useContext(WalletAuthContext);

export function WalletAuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const supabase = useSupabaseClient<SupabaseClient>();

  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';

  // Client-side mounting state
  const [mounted, setMounted] = useState(false);

  // Wait until after client-side hydration to mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Wallet state from wagmi (call hooks unconditionally but handle values conditionally)
  const accountData = useAccount();
  const signMessageData = useSignMessage();
  const disconnectData = useDisconnect();
  const chainIdData = useChainId();

  // Use the data only when mounted and in browser
  const { address, isConnected } = isBrowser && mounted ? accountData : { address: undefined, isConnected: false };
  const { signMessageAsync } = isBrowser && mounted ? signMessageData : { signMessageAsync: undefined };
  const { disconnect } = isBrowser && mounted ? disconnectData : { disconnect: undefined };
  const currentChainId = isBrowser && mounted ? chainIdData : 1;

  // Auth state
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [walletError, setWalletError] = useState<WalletError | null>(null);
  const [connectingWallet, setConnectingWallet] = useState(false);
  const [signingIn, setSigningIn] = useState(false);

  // Check if the user has a complete profile
  const isProfileComplete = !!user && !!user.full_name && !!user.email;

  // Get KYC status
  const kycStatus = user?.kyc_status || 'none';

  // Check if the user is authenticated
  const isAuthenticated = !!session && !!user;

  // Clear wallet error
  const clearWalletError = () => setWalletError(null);

  // Fetch the user's session on component mount
  useEffect(() => {
    const getSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
        } else {
          setSession(data.session);
        }
      } catch (err) {
        console.error('Unexpected error getting session:', err);
        setError('Failed to get session');
      } finally {
        setLoading(false);
      }
    };

    getSession();
  }, [supabase.auth]);

  // Fetch the user's profile when the session changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!session?.user?.id) {
        console.log('WalletAuth: No session or user ID, clearing user profile');
        setUser(null);
        setLoading(false);
        return;
      }

      // Prevent duplicate fetches
      if (user && user.id === session.user.id) {
        console.log('WalletAuth: Profile already loaded for this user');
        setLoading(false);
        return;
      }

      try {
        console.log('WalletAuth: Fetching user profile for ID:', session.user.id);
        setLoading(true);

        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (error) {
          console.error('WalletAuth: Error fetching user profile:', error);

          // If profile doesn't exist, try to create it
          if (error.code === 'PGRST116') {
            console.log('WalletAuth: Profile not found, attempting to create...');
            const walletAddress = session.user.user_metadata?.wallet_address;

            if (walletAddress) {
              const { data: newProfile, error: createError } = await supabase
                .from('profiles')
                .insert({
                  id: session.user.id,
                  wallet_address: walletAddress.toLowerCase(),
                  kyc_status: 'none',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                })
                .select()
                .single();

              if (createError) {
                console.error('WalletAuth: Error creating profile:', createError);
                setError(`Failed to create profile: ${createError.message}`);
                setUser(null);
              } else {
                console.log('WalletAuth: Profile created successfully:', newProfile);
                setUser(newProfile as UserProfile);
                setError(null);
              }
            } else {
              setError('No wallet address found in user metadata');
              setUser(null);
            }
          } else {
            setError(`Profile fetch error: ${error.message}`);
            setUser(null);
          }
        } else {
          console.log('WalletAuth: Profile fetched successfully:', data);
          setUser(data as UserProfile);
          setError(null);
        }
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error('WalletAuth: Unexpected error fetching user profile:', err);
        setError(`Failed to fetch user profile: ${errorMessage}`);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, [session?.user?.id, supabase]); // Only depend on user ID, not the full session object

  // Sign in with wallet
  const signInWithWallet = async () => {
    try {
      console.log('WalletAuth: Starting wallet sign-in process...');

      // Clear previous errors
      setError(null);
      setWalletError(null);

      if (!address || !isConnected || !signMessageAsync) {
        console.error('WalletAuth: Wallet not properly connected', {
          address,
          isConnected,
          signMessageAsync: !!signMessageAsync
        });
        const error = parseWalletError(new Error('Wallet not connected'));
        setWalletError(error);
        return;
      }

      console.log('WalletAuth: Wallet connected, starting authentication for address:', address);
      setSigningIn(true);

      // Create SIWE message with proper formatting
      // Generate a proper alphanumeric nonce (required by EIP-4361)
      const nonce = Array.from(crypto.getRandomValues(new Uint8Array(16)))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      // Use the current chain ID from wagmi hook

      // Create SIWE message with all required fields
      const siweParams = {
        domain: window.location.host,
        address,
        statement: 'Sign in with Ethereum to access BrickChain platform.',
        uri: window.location.origin,
        version: '1',
        chainId: currentChainId,
        nonce,
        issuedAt: new Date().toISOString(),
      };

      console.log('Creating SIWE message with params:', siweParams);

      let message;
      try {
        message = new SiweMessage(siweParams);
      } catch (siweError) {
        console.error('Error creating SIWE message:', siweError);
        const errorMessage = siweError instanceof Error ? siweError.message : 'Unknown error';
        throw new Error(`Failed to create SIWE message: ${errorMessage}`);
      }

      const signMessage = message.prepareMessage();
      console.log('SIWE message prepared:', signMessage);

      // Sign the message with retry logic
      let signature;
      try {
        console.log('WalletAuth: Requesting signature from wallet...');
        signature = await retryWalletConnection(
          async () => await signMessageAsync({ message: signMessage }),
          3, // Max 3 attempts
          1000 // Base delay of 1 second
        );
        console.log('WalletAuth: Signature received successfully');
      } catch (signError) {
        console.error('WalletAuth: Signature failed:', signError);
        // Handle signature errors
        const parsedError = parseWalletError(signError);
        setWalletError(parsedError);
        return;
      }

      // Verify the signature client-side and create/authenticate user
      console.log('WalletAuth: Processing authentication with signature...');

      // Check if user exists with this wallet address
      console.log('WalletAuth: Checking for existing profile with wallet address:', address.toLowerCase());
      const { data: existingProfile, error: profileError } = await supabase
        .from('profiles')
        .select('id, wallet_address')
        .eq('wallet_address', address.toLowerCase())
        .single();

      console.log('WalletAuth: Profile query result:', { existingProfile, profileError });
      let userId;

      if (profileError && profileError.code === 'PGRST116') {
        // User doesn't exist, create a new one
        console.log('Creating new user for wallet:', address);

        // Create auth user
        const { data: authUser, error: authError } = await supabase.auth.signUp({
          email: `${address.toLowerCase()}@wallet.brickchain.com`,
          password: signature.slice(0, 32), // Use part of signature as password
          options: {
            data: {
              wallet_address: address.toLowerCase(),
              is_wallet_user: true
            }
          }
        });

        if (authError) {
          throw new Error(`Failed to create user: ${authError.message}`);
        }

        userId = authUser.user?.id;

        if (userId) {
          // Create profile
          const { error: insertError } = await supabase
            .from('profiles')
            .insert({
              id: userId,
              wallet_address: address.toLowerCase(),
              kyc_status: 'none',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error('Error creating profile:', insertError);
          }
        }

        console.log('WalletAuth: Setting session for new user:', authUser.user?.id);
        setSession(authUser.session);
      } else if (existingProfile) {
        // User exists, sign them in
        console.log('WalletAuth: Signing in existing user:', existingProfile.id);
        userId = existingProfile.id;

        // Sign in with email and signature-based password
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: `${address.toLowerCase()}@wallet.brickchain.com`,
          password: signature.slice(0, 32)
        });

        if (signInError) {
          // If password doesn't match, update it
          console.log('WalletAuth: Password mismatch, updating user password...');
          const { error: updateError } = await supabase.auth.updateUser({
            password: signature.slice(0, 32)
          });

          if (updateError) {
            console.error('WalletAuth: Failed to update password:', updateError);
            throw new Error(`Failed to authenticate: ${updateError.message}`);
          }

          // Try signing in again
          const { data: retrySignIn, error: retryError } = await supabase.auth.signInWithPassword({
            email: `${address.toLowerCase()}@wallet.brickchain.com`,
            password: signature.slice(0, 32)
          });

          if (retryError) {
            console.error('WalletAuth: Retry sign-in failed:', retryError);
            throw new Error(`Failed to sign in: ${retryError.message}`);
          }

          console.log('WalletAuth: Retry sign-in successful');
          setSession(retrySignIn.session);
        } else {
          console.log('WalletAuth: Sign-in successful');
          setSession(signInData.session);
        }
      } else {
        console.error('WalletAuth: Failed to query user profile');
        throw new Error('Failed to query user profile');
      }

      // Fetch the user profile directly to check completion status
      console.log('WalletAuth: Fetching user profile for redirect decision...');

      let userProfile = null;
      try {
        // Get the current session to ensure we have the user ID
        const { data: currentSession } = await supabase.auth.getSession();
        const currentUserId = currentSession?.session?.user?.id || userId;

        if (currentUserId) {
          const { data: profileData, error: profileFetchError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', currentUserId)
            .single();

          if (profileFetchError) {
            console.error('WalletAuth: Error fetching profile for redirect:', profileFetchError);
          } else {
            userProfile = profileData;
            console.log('WalletAuth: Profile fetched for redirect:', userProfile);
          }
        }
      } catch (fetchError) {
        console.error('WalletAuth: Error in profile fetch:', fetchError);
      }

      console.log('WalletAuth: Authentication completed successfully');

      // Check profile completion status
      const profileComplete = userProfile && userProfile.full_name && userProfile.email;

      console.log('WalletAuth: Profile completion check:', {
        hasProfile: !!userProfile,
        hasFullName: !!userProfile?.full_name,
        hasEmail: !!userProfile?.email,
        isComplete: profileComplete
      });

      // Redirect based on profile completion
      if (!profileComplete) {
        console.log('WalletAuth: Profile incomplete, redirecting to profile completion');
        router.push('/complete-profile');
      } else {
        console.log('WalletAuth: Profile complete, redirecting to dashboard');
        router.push('/dashboard');
      }
    } catch (err: any) {
      console.error('Error signing in with wallet:', err);
      setError(err.message || 'Failed to sign in with wallet');

      // Parse the error for better user feedback
      const parsedError = parseWalletError(err);
      setWalletError(parsedError);
    } finally {
      setSigningIn(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      setWalletError(null);

      console.log('Starting sign out process...');

      // Clear user state first
      setUser(null);
      setSession(null);

      // Sign out from Supabase
      const { error: signOutError } = await supabase.auth.signOut();
      if (signOutError) {
        console.error('Supabase sign out error:', signOutError);
      }

      // Disconnect wallet if available
      if (disconnect) {
        try {
          disconnect();
          console.log('Wallet disconnected successfully');
        } catch (disconnectError) {
          console.error('Error disconnecting wallet:', disconnectError);
        }
      }

      // Clear local storage and cookies only in browser environment
      if (typeof window !== 'undefined') {
        try {
          // Clear specific wallet-related items first
          const walletKeys = [
            'wagmi.connected',
            'wagmi.wallet',
            'wagmi.store',
            'rainbowkit.recent',
            'rainbowkit.wallet'
          ];

          walletKeys.forEach(key => {
            localStorage.removeItem(key);
          });

          // Clear RainbowKit and WalletConnect data
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('rk-') ||
                key.startsWith('wagmi-') ||
                key.startsWith('walletconnect') ||
                key.startsWith('wc@2:') ||
                key.includes('rainbow')) {
              localStorage.removeItem(key);
            }
          });

          // Clear session cookies
          document.cookie.split(';').forEach(cookie => {
            const [name] = cookie.trim().split('=');
            if (name) {
              // Clear for current domain
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
              // Clear for parent domain
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
            }
          });

          console.log('Local storage and cookies cleared');
        } catch (storageError) {
          console.error('Error clearing storage:', storageError);
        }
      }

      // Force page reload to ensure clean state
      setTimeout(() => {
        window.location.href = '/';
      }, 100);

    } catch (err: any) {
      console.error('Error signing out:', err);
      setError(err.message || 'Failed to sign out');

      // Even if there's an error, try to redirect
      setTimeout(() => {
        window.location.href = '/';
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  // Refresh user profile
  const refreshUser = async () => {
    if (!session?.user?.id) {
      setUser(null);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (error) {
        console.error('Error refreshing user profile:', error);
        setError(error.message);
        setUser(null);
      } else {
        setUser(data as UserProfile);
      }
    } catch (err: any) {
      console.error('Unexpected error refreshing user profile:', err);
      setError(err.message || 'Failed to refresh user profile');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // If not mounted yet, provide default values to avoid SSR issues
  const contextValue = {
    user,
    session,
    loading,
    isWalletConnected: mounted ? isConnected : false,
    walletAddress: mounted ? address : undefined,
    isAuthenticated,
    signInWithWallet,
    signOut,
    refreshUser,
    isProfileComplete,
    kycStatus,
    error,
    walletError,
    clearWalletError,
    connectingWallet,
    signingIn,
  };

  return (
    <WalletAuthContext.Provider value={contextValue}>
      {children}
    </WalletAuthContext.Provider>
  );
}
