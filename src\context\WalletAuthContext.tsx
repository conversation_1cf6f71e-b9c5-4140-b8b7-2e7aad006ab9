'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAccount, useSignMessage, useDisconnect } from 'wagmi';
import { SiweMessage } from 'siwe';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import type { SupabaseClient, Session } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { parseWalletError, type WalletError, retryWalletConnection } from '@/utils/walletErrorHandling';

export type UserProfile = {
  id: string;
  wallet_address: string;
  full_name: string | null;
  email: string | null;
  phone: string | null;
  kyc_status: 'none' | 'pending' | 'verified' | 'rejected';
  created_at: string;
  updated_at: string;
  profile_picture_url?: string;
};

type WalletAuthContextType = {
  // User state
  user: UserProfile | null;
  session: Session | null;
  loading: boolean;

  // Wallet state
  isWalletConnected: boolean;
  walletAddress: string | undefined;

  // Authentication state
  isAuthenticated: boolean;

  // Authentication actions
  signInWithWallet: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;

  // Profile completion status
  isProfileComplete: boolean;

  // KYC status
  kycStatus: 'none' | 'pending' | 'verified' | 'rejected';

  // Error state
  error: string | null;
  walletError: WalletError | null;
  clearWalletError: () => void;

  // Loading states
  connectingWallet: boolean;
  signingIn: boolean;
};

const WalletAuthContext = createContext<WalletAuthContextType>({
  user: null,
  session: null,
  loading: true,
  isWalletConnected: false,
  walletAddress: undefined,
  isAuthenticated: false,
  signInWithWallet: async () => {},
  signOut: async () => {},
  refreshUser: async () => {},
  isProfileComplete: false,
  kycStatus: 'none',
  error: null,
  walletError: null,
  clearWalletError: () => {},
  connectingWallet: false,
  signingIn: false,
});

export const useWalletAuth = () => useContext(WalletAuthContext);

export function WalletAuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const supabase = useSupabaseClient<SupabaseClient>();

  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';

  // Client-side mounting state
  const [mounted, setMounted] = useState(false);

  // Wait until after client-side hydration to mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Wallet state from wagmi (only when mounted and in browser)
  const { address, isConnected } = isBrowser && mounted ? useAccount() : { address: undefined, isConnected: false };
  const { signMessageAsync } = isBrowser && mounted ? useSignMessage() : { signMessageAsync: undefined };
  const { disconnect } = isBrowser && mounted ? useDisconnect() : { disconnect: undefined };

  // Auth state
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [walletError, setWalletError] = useState<WalletError | null>(null);
  const [connectingWallet, setConnectingWallet] = useState(false);
  const [signingIn, setSigningIn] = useState(false);

  // Check if the user has a complete profile
  const isProfileComplete = !!user && !!user.full_name && !!user.email;

  // Get KYC status
  const kycStatus = user?.kyc_status || 'none';

  // Check if the user is authenticated
  const isAuthenticated = !!session && !!user;

  // Clear wallet error
  const clearWalletError = () => setWalletError(null);

  // Fetch the user's session on component mount
  useEffect(() => {
    const getSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
        } else {
          setSession(data.session);
        }
      } catch (err) {
        console.error('Unexpected error getting session:', err);
        setError('Failed to get session');
      } finally {
        setLoading(false);
      }
    };

    getSession();
  }, [supabase.auth]);

  // Fetch the user's profile when the session changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!session?.user?.id) {
        setUser(null);
        return;
      }

      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);
          setError(error.message);
          setUser(null);
        } else {
          setUser(data as UserProfile);
        }
      } catch (err) {
        console.error('Unexpected error fetching user profile:', err);
        setError('Failed to fetch user profile');
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, [session, supabase]);

  // Sign in with wallet
  const signInWithWallet = async () => {
    try {
      // Clear previous errors
      setError(null);
      setWalletError(null);

      if (!address || !isConnected || !signMessageAsync) {
        const error = parseWalletError(new Error('Wallet not connected'));
        setWalletError(error);
        return;
      }

      setSigningIn(true);

      // Create SIWE message
      const nonce = Math.floor(Math.random() * 1000000).toString();
      const message = new SiweMessage({
        domain: window.location.host,
        address,
        statement: 'Sign in with Ethereum to BrickChain',
        uri: window.location.origin,
        version: '1',
        chainId: 1, // Replace with your chain ID
        nonce,
      });

      const signMessage = message.prepareMessage();

      // Sign the message with retry logic
      let signature;
      try {
        signature = await retryWalletConnection(
          async () => await signMessageAsync({ message: signMessage }),
          3, // Max 3 attempts
          1000 // Base delay of 1 second
        );
      } catch (signError) {
        // Handle signature errors
        const parsedError = parseWalletError(signError);
        setWalletError(parsedError);
        return;
      }

      // Verify the signature with Supabase
      const { data, error } = await supabase.functions.invoke('auth-wallet-login', {
        body: {
          message: signMessage,
          signature,
          wallet_address: address,
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      // Set the session
      if (data?.session) {
        setSession(data.session);
      }

      // Refresh the user profile
      await refreshUser();

      // Redirect to profile completion if needed
      if (!isProfileComplete) {
        router.push('/complete-profile');
      } else {
        router.push('/dashboard');
      }
    } catch (err: any) {
      console.error('Error signing in with wallet:', err);
      setError(err.message || 'Failed to sign in with wallet');

      // Parse the error for better user feedback
      const parsedError = parseWalletError(err);
      setWalletError(parsedError);
    } finally {
      setSigningIn(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);

      // Sign out from Supabase
      await supabase.auth.signOut();

      // Disconnect wallet if available
      if (disconnect) {
        disconnect();
      }

      // Clear user state
      setUser(null);
      setSession(null);

      // Clear local storage and cookies only in browser environment
      if (typeof window !== 'undefined') {
        // Clear local storage
        localStorage.clear();

        // Clear session cookies
        document.cookie.split(';').forEach(cookie => {
          const [name] = cookie.trim().split('=');
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        });
      }

      // Redirect to home page
      router.push('/');
    } catch (err: any) {
      console.error('Error signing out:', err);
      setError(err.message || 'Failed to sign out');
    } finally {
      setLoading(false);
    }
  };

  // Refresh user profile
  const refreshUser = async () => {
    if (!session?.user?.id) {
      setUser(null);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (error) {
        console.error('Error refreshing user profile:', error);
        setError(error.message);
        setUser(null);
      } else {
        setUser(data as UserProfile);
      }
    } catch (err: any) {
      console.error('Unexpected error refreshing user profile:', err);
      setError(err.message || 'Failed to refresh user profile');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // If not mounted yet, provide default values to avoid SSR issues
  const contextValue = {
    user,
    session,
    loading,
    isWalletConnected: mounted ? isConnected : false,
    walletAddress: mounted ? address : undefined,
    isAuthenticated,
    signInWithWallet,
    signOut,
    refreshUser,
    isProfileComplete,
    kycStatus,
    error,
    walletError,
    clearWalletError,
    connectingWallet,
    signingIn,
  };

  return (
    <WalletAuthContext.Provider value={contextValue}>
      {children}
    </WalletAuthContext.Provider>
  );
}
