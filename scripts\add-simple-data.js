#!/usr/bin/env node

/**
 * Add Simple Sample Data
 *
 * This script adds sample data that doesn't require auth users.
 * For testing the app structure and UI components.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration - Use service role for bypassing RLS
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function createAuthUsers() {
  console.log(colorize('👤 Creating auth users...', 'blue'));

  const authUsers = [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      email: '<EMAIL>'
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      email: '<EMAIL>'
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      email: '<EMAIL>'
    }
  ];

  try {
    // Insert directly into auth.users table
    for (const user of authUsers) {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
              VALUES ('${user.id}', '${user.email}', now(), now(), now())
              ON CONFLICT (id) DO NOTHING`
      });

      if (error) {
        console.log(colorize(`⚠️  Auth user ${user.email} might already exist`, 'yellow'));
      }
    }

    console.log(colorize(`✅ Auth users created/verified`, 'green'));
    return true;
  } catch (error) {
    console.error(colorize('❌ Error creating auth users:', 'red'), error.message);
    return false;
  }
}

async function addSampleUsers() {
  console.log(colorize('👥 Adding sample users...', 'blue'));

  const sampleUsers = [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      wallet_address: '******************************************',
      email: '<EMAIL>',
      role: 'investor',
      kyc_status: 'verified'
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      wallet_address: '0x8ba1f109551bD432803012645Hac136c30C6756',
      email: '<EMAIL>',
      role: 'property_owner',
      kyc_status: 'verified'
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      wallet_address: '******************************************',
      email: '<EMAIL>',
      role: 'admin',
      kyc_status: 'verified'
    }
  ];

  try {
    const { data, error } = await supabase
      .from('users')
      .insert(sampleUsers)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Added ${data.length} users`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error adding users:', 'red'), error.message);
    return [];
  }
}

async function addSampleProperties() {
  console.log(colorize('🏠 Adding sample properties...', 'blue'));

  const sampleProperties = [
    {
      name: 'Luxury Downtown Apartment',
      description: 'A stunning 2-bedroom apartment in the heart of downtown with panoramic city views.',
      price: 500000,
      location: 'Downtown, City Center',
      property_type: 'apartment',
      bedrooms: 2,
      bathrooms: 2,
      square_feet: 1200,
      status: 'active',
      owner_id: '550e8400-e29b-41d4-a716-446655440002',
      images: ['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800'],
      metadata: {
        year_built: 2018,
        parking_spaces: 1,
        amenities: ['gym', 'pool', 'concierge']
      }
    },
    {
      name: 'Suburban Family Home',
      description: 'Perfect family home with large backyard and excellent school district.',
      price: 750000,
      location: 'Suburban Heights',
      property_type: 'house',
      bedrooms: 4,
      bathrooms: 3,
      square_feet: 2500,
      status: 'active',
      owner_id: '550e8400-e29b-41d4-a716-446655440002',
      images: ['https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800'],
      metadata: {
        year_built: 2010,
        parking_spaces: 2,
        amenities: ['garden', 'garage', 'fireplace']
      }
    },
    {
      name: 'Modern Condo with City View',
      description: 'Stunning modern condo with panoramic city views and premium finishes.',
      price: 650000,
      location: 'Midtown District',
      property_type: 'condo',
      bedrooms: 3,
      bathrooms: 2,
      square_feet: 1800,
      status: 'active',
      owner_id: '550e8400-e29b-41d4-a716-446655440002',
      images: ['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800'],
      metadata: {
        year_built: 2020,
        parking_spaces: 1,
        amenities: ['rooftop_deck', 'gym', 'doorman']
      }
    }
  ];

  try {
    const { data, error } = await supabase
      .from('properties')
      .insert(sampleProperties)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Added ${data.length} properties`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error adding properties:', 'red'), error.message);
    return [];
  }
}

async function showDataSummary() {
  console.log(colorize('\n📊 Current Database Contents:', 'bright'));

  try {
    const { data: users } = await supabase.from('users').select('email, role, kyc_status');
    const { data: properties } = await supabase.from('properties').select('name, price, status');

    console.log(colorize('\n👥 Users:', 'blue'));
    if (users && users.length > 0) {
      users.forEach(user => {
        console.log(`   • ${user.email} (${user.role}) - KYC: ${user.kyc_status}`);
      });
    } else {
      console.log('   No users found');
    }

    console.log(colorize('\n🏠 Properties:', 'blue'));
    if (properties && properties.length > 0) {
      properties.forEach(property => {
        console.log(`   • ${property.name} - $${property.price.toLocaleString()} (${property.status})`);
      });
    } else {
      console.log('   No properties found');
    }

  } catch (error) {
    console.error(colorize('❌ Error getting summary:', 'red'), error.message);
  }
}

async function main() {
  console.log(colorize('\n🌱 Adding Simple Sample Data', 'bright'));
  console.log('─'.repeat(50));

  try {
    // Create auth users first
    const authSuccess = await createAuthUsers();
    if (!authSuccess) {
      console.log(colorize('⚠️  Continuing without auth users...', 'yellow'));
    }

    // Add sample data
    const users = await addSampleUsers();
    const properties = await addSampleProperties();

    console.log(colorize('\n🎉 Sample data added successfully!', 'green'));

    await showDataSummary();

    console.log(colorize('\n🚀 Next Steps:', 'bright'));
    console.log('1. Start your Next.js app: npm run dev');
    console.log('2. Visit: http://localhost:3000');
    console.log('3. Check the marketplace and dashboard pages');
    console.log('4. View data in Studio: http://127.0.0.1:54323');

  } catch (error) {
    console.error(colorize('\n❌ Failed to add sample data:', 'red'), error.message);
    process.exit(1);
  }
}

main();
