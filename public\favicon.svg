<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="brickGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EF4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="16" cy="16" r="16" fill="url(#bgGradient)"/>
  
  <!-- Building/Brick structure -->
  <!-- Bottom row -->
  <rect x="6" y="22" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="11" y="22" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="16" y="22" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="21" y="22" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  
  <!-- Second row (offset) -->
  <rect x="4" y="18" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="9" y="18" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="14" y="18" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="19" y="18" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="24" y="18" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  
  <!-- Third row -->
  <rect x="6" y="14" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="11" y="14" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="16" y="14" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  <rect x="21" y="14" width="4" height="3" fill="url(#brickGradient)" rx="0.5"/>
  
  <!-- Blockchain link symbol (simplified chain) -->
  <circle cx="10" cy="10" r="2.5" fill="none" stroke="#FBBF24" stroke-width="1.5"/>
  <circle cx="16" cy="8" r="2.5" fill="none" stroke="#FBBF24" stroke-width="1.5"/>
  <circle cx="22" cy="10" r="2.5" fill="none" stroke="#FBBF24" stroke-width="1.5"/>
  
  <!-- Connection lines -->
  <line x1="12.2" y1="9.2" x2="13.8" y2="8.8" stroke="#FBBF24" stroke-width="1.5"/>
  <line x1="18.2" y1="8.8" x2="19.8" y2="9.2" stroke="#FBBF24" stroke-width="1.5"/>
</svg>
