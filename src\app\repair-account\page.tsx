'use client';
import React, { useState } from 'react';
import { createClient } from '@supabase/supabase-js';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowRight, Mail, Lock, User, Phone, Building, Briefcase, Loader2 } from 'lucide-react';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function RepairAccount() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [role, setRole] = useState('investor');
  const [companyName, setCompanyName] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [userId, setUserId] = useState('');

  const handleAuthenticate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      console.log('Authenticating user...');
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (authError) {
        console.error('Authentication failed:', authError);
        setError('Authentication failed: ' + authError.message);
        return;
      }

      if (!authData.user) {
        console.error('No user found after authentication');
        setError('No user account found. Please sign up first.');
        return;
      }

      console.log('User authenticated:', authData.user.id);
      setUserId(authData.user.id);

      // Check if profile exists
      console.log('Checking for existing profile...');
      const { data: existingProfile, error: profileCheckError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .maybeSingle();

      if (profileCheckError) {
        console.error('Error checking for profile:', profileCheckError);
        setError('Error checking for profile: ' + profileCheckError.message);
        return;
      }

      if (existingProfile) {
        console.log('Profile already exists:', existingProfile);
        setError('Your profile is already configured correctly. No repair needed.');

        // Redirect to home page after 3 seconds
        setTimeout(() => {
          router.push('/');
        }, 3000);

        return;
      }

      // Move to step 2 to collect profile details
      setStep(2);

    } catch (err) {
      console.error('Authentication error:', err);
      setError('An error occurred during authentication: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (!userId) {
        setError('User ID is missing. Please try the process again.');
        return;
      }

      if (!fullName || !phone || !role) {
        setError('Please fill in all required fields.');
        return;
      }

      if (role === 'owner' && !companyName) {
        setError('Company name is required for property owners.');
        return;
      }

      // Create profile
      console.log('Creating profile for user:', userId);
      const { error: createError } = await supabase
        .from('profiles')
        .insert([
          {
            id: userId,
            full_name: fullName,
            phone: phone,
            role: role,
            company_name: role === 'owner' ? companyName : null,
            created_at: new Date().toISOString()
          }
        ]);

      if (createError) {
        console.error('Error creating profile:', createError);
        setError('Error creating profile: ' + createError.message);
        return;
      }

      console.log('Profile created successfully!');
      setSuccess('Your profile has been repaired successfully. You will be redirected to the home page.');

      // Redirect to home page after 3 seconds
      setTimeout(() => {
        router.push('/');
      }, 3000);

    } catch (err) {
      console.error('Profile creation error:', err);
      setError('An error occurred while creating profile: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 flex items-center justify-center p-6">
      {/* Hidden data for testing */}
      <div hidden data-supabase-url={supabaseUrl} data-supabase-key={supabaseAnonKey}></div>

      <div className="w-full max-w-md">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-white mb-2">Repair Account</h2>
          <p className="text-gray-400">Fix your account if you have login issues</p>
        </div>

        {step === 1 ? (
          <form onSubmit={handleAuthenticate} className="space-y-6 bg-gray-900/50 p-6 rounded-lg border border-gray-800">
            <p className="text-gray-300 text-sm">
              This utility helps repair accounts that were created but are missing a profile.
              First, verify your credentials to authenticate your account.
            </p>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Email Address</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Password</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="••••••••"
                  required
                />
              </div>
            </div>

            {error && (
              <div className="p-3 bg-red-900/40 border border-red-800 rounded-lg">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-lg text-white font-medium flex items-center justify-center space-x-2 shadow-lg shadow-purple-900/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all"
            >
              {loading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Verifying...</span>
                </>
              ) : (
                <>
                  <span>Verify Account</span>
                  <ArrowRight className="h-5 w-5" />
                </>
              )}
            </button>

            <div className="text-center pt-4">
              <Link href="/" className="text-purple-400 hover:text-purple-300 text-sm">
                Back to Home
              </Link>
            </div>
          </form>
        ) : (
          <form onSubmit={handleCreateProfile} className="space-y-6 bg-gray-900/50 p-6 rounded-lg border border-gray-800">
            <p className="text-gray-300 text-sm">
              Your account is authenticated but missing a profile. Please provide the following information to create your profile.
            </p>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Full Name</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="Your full name"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Phone Number</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="Your phone number"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Your Role</label>
              <div className="flex flex-col space-y-3">
                <label className="relative flex items-center p-3 rounded-lg border border-gray-700 bg-gray-900/80 cursor-pointer hover:bg-gray-800/60 transition-colors">
                  <input
                    type="radio"
                    name="role"
                    value="investor"
                    checked={role === 'investor'}
                    onChange={() => setRole('investor')}
                    className="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-700 rounded-full"
                  />
                  <div className="ml-3 flex items-center">
                    <Briefcase className="h-5 w-5 text-purple-500 mr-2" />
                    <span className="text-white font-medium">Investor</span>
                  </div>
                  <span className="ml-auto text-xs text-gray-400">Invest in properties</span>
                </label>

                <label className="relative flex items-center p-3 rounded-lg border border-gray-700 bg-gray-900/80 cursor-pointer hover:bg-gray-800/60 transition-colors">
                  <input
                    type="radio"
                    name="role"
                    value="owner"
                    checked={role === 'owner'}
                    onChange={() => setRole('owner')}
                    className="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-700 rounded-full"
                  />
                  <div className="ml-3 flex items-center">
                    <Building className="h-5 w-5 text-purple-500 mr-2" />
                    <span className="text-white font-medium">Property Owner</span>
                  </div>
                  <span className="ml-auto text-xs text-gray-400">List properties</span>
                </label>
              </div>
            </div>

            {role === 'owner' && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300 block">Company Name</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Building className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    type="text"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                    placeholder="Your company name"
                    required={role === 'owner'}
                  />
                </div>
              </div>
            )}

            {error && (
              <div className="p-3 bg-red-900/40 border border-red-800 rounded-lg">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            )}

            {success && (
              <div className="p-3 bg-green-900/40 border border-green-800 rounded-lg">
                <p className="text-green-300 text-sm">{success}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={loading || !!success}
              className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-lg text-white font-medium flex items-center justify-center space-x-2 shadow-lg shadow-purple-900/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all"
            >
              {loading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Creating Profile...</span>
                </>
              ) : (
                <span>Create Profile</span>
              )}
            </button>
          </form>
        )}
      </div>
    </div>
  );
}