-- <PERSON><PERSON><PERSON>n Hosted Database Setup
-- Complete schema setup for wallet-based authentication and property management
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Step 1: Drop everything (tables, functions, triggers, policies)
DROP TABLE IF EXISTS public.investments CASCADE;
DROP TABLE IF EXISTS public.property_images CASCADE;
DROP TABLE IF EXISTS public.properties CASCADE;
DROP TABLE IF EXISTS public.kyc_documents CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS user_role_type CASCADE;
DROP TYPE IF EXISTS kyc_status_type CASCADE;
DROP TYPE IF EXISTS property_status_type CASCADE;
DROP TYPE IF EXISTS investment_status_type CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;

-- Step 2: Create custom types
CREATE TYPE user_role_type AS ENUM ('investor', 'property_owner', 'admin');
CREATE TYPE kyc_status_type AS ENUM ('none', 'pending', 'verified', 'rejected');
CREATE TYPE property_status_type AS ENUM ('draft', 'active', 'available', 'pending', 'funded', 'sold', 'inactive', 'archived');
CREATE TYPE investment_status_type AS ENUM ('pending', 'completed', 'failed', 'cancelled', 'refunded');

-- Step 3: Create profiles table (this is what the app expects)
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_address TEXT UNIQUE,
  full_name TEXT,
  email TEXT,
  phone TEXT,
  kyc_status kyc_status_type DEFAULT 'none',
  profile_picture_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 4: Create properties table
CREATE TABLE public.properties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(15,2) NOT NULL,
  location TEXT NOT NULL,
  property_type TEXT NOT NULL,
  bedrooms INTEGER,
  bathrooms INTEGER,
  square_feet INTEGER,
  status property_status_type DEFAULT 'draft',
  owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  images TEXT[], -- Array of image URLs
  documents TEXT[], -- Array of document URLs
  metadata JSONB DEFAULT '{}',
  return_rate DECIMAL(5,2) DEFAULT 8.0,
  image_url TEXT,
  published BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 5: Create investments table
CREATE TABLE public.investments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  investor_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  amount DECIMAL(15,2) NOT NULL,
  shares INTEGER NOT NULL DEFAULT 1,
  transaction_hash TEXT,
  status investment_status_type DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 6: Create property_images table
CREATE TABLE public.property_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  image_cid TEXT,
  thumbnail_url TEXT,
  image_type VARCHAR(50) NOT NULL, -- 'primary', 'additional', 'floorplan', 'document'
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  width INTEGER,
  height INTEGER,
  optimized BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 7: Create KYC documents table
CREATE TABLE public.kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL,
  document_category TEXT NOT NULL, -- 'identity', 'address', 'financial'
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status kyc_status_type DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 8: Create utility functions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 9: Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_properties_updated_at
BEFORE UPDATE ON public.properties
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_investments_updated_at
BEFORE UPDATE ON public.investments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_property_images_updated_at
BEFORE UPDATE ON public.property_images
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kyc_documents_updated_at
BEFORE UPDATE ON public.kyc_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Step 10: Create indexes for performance
CREATE INDEX profiles_wallet_address_idx ON public.profiles (wallet_address);
CREATE INDEX profiles_kyc_status_idx ON public.profiles (kyc_status);
CREATE INDEX properties_owner_id_idx ON public.properties (owner_id);
CREATE INDEX properties_status_idx ON public.properties (status);
CREATE INDEX properties_published_idx ON public.properties (published);
CREATE INDEX investments_property_id_idx ON public.investments (property_id);
CREATE INDEX investments_investor_id_idx ON public.investments (investor_id);
CREATE INDEX investments_status_idx ON public.investments (status);
CREATE INDEX property_images_property_id_idx ON public.property_images (property_id);
CREATE INDEX kyc_documents_user_id_idx ON public.kyc_documents (user_id);

-- Step 11: Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kyc_documents ENABLE ROW LEVEL SECURITY;

-- Step 12: Create RLS policies for profiles table
CREATE POLICY read_own_profile ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY update_own_profile ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY insert_own_profile ON public.profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Step 13: Create RLS policies for properties table
CREATE POLICY read_all_properties ON public.properties
  FOR SELECT
  USING (published = true OR auth.uid() = owner_id);

CREATE POLICY manage_own_properties ON public.properties
  FOR ALL
  USING (auth.uid() = owner_id);

-- Step 14: Create RLS policies for investments table
CREATE POLICY read_own_investments ON public.investments
  FOR SELECT
  USING (auth.uid() = investor_id);

CREATE POLICY insert_own_investments ON public.investments
  FOR INSERT
  WITH CHECK (auth.uid() = investor_id);

-- Step 15: Create RLS policies for property_images table
CREATE POLICY read_property_images ON public.property_images
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.properties p
      WHERE p.id = property_id AND (p.published = true OR auth.uid() = p.owner_id)
    )
  );

CREATE POLICY manage_property_images ON public.property_images
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.properties p
      WHERE p.id = property_id AND auth.uid() = p.owner_id
    )
  );

-- Step 16: Create RLS policies for kyc_documents table
CREATE POLICY read_own_documents ON public.kyc_documents
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY insert_own_documents ON public.kyc_documents
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Step 17: Create a function to handle new user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email)
  VALUES (NEW.id, NEW.email);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 18: Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Step 19: Show completion status
SELECT 'BrickChain Database Setup Complete!' as status;
SELECT 'Schema created successfully. Ready for wallet authentication.' as message;
