'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, Users, Building, DollarSign, Loader2 } from 'lucide-react';
import { createClient } from '@supabase/supabase-js';

interface TransactionStat {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  change?: string;
  isPositive?: boolean;
}

// Create Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function TransactionStats() {
  const [stats, setStats] = useState<TransactionStat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      try {
        setLoading(true);

        // Fetch total investments
        const { data: investmentsData, error: investmentsError } = await supabase
          .from('investments')
          .select('amount');

        if (investmentsError) {
          console.warn('Error fetching investments:', investmentsError);
        }

        // Calculate total investment amount
        const totalInvestment = investmentsData?.reduce((sum, item) => sum + (item.amount || 0), 0) || 0;

        // Fetch total users
        const { count: userCount, error: usersError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        if (usersError) {
          console.warn('Error fetching users:', usersError);
        }

        // Fetch total properties
        const { count: propertyCount, error: propertiesError } = await supabase
          .from('properties')
          .select('*', { count: 'exact', head: true });

        if (propertiesError) {
          console.warn('Error fetching properties:', propertiesError);
        }

        // Use investments as transactions since we don't have a separate transactions table
        const { count: transactionCount, error: transactionsError } = await supabase
          .from('investments')
          .select('*', { count: 'exact', head: true });

        if (transactionsError) {
          console.warn('Error fetching investments:', transactionsError);
        }

        // Set the stats
        setStats([
          {
            icon: <DollarSign className="h-6 w-6 text-green-400" />,
            label: 'Total Invested',
            value: new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              maximumFractionDigits: 0
            }).format(totalInvestment),
            change: '+12.5%',
            isPositive: true
          },
          {
            icon: <Users className="h-6 w-6 text-blue-400" />,
            label: 'Active Users',
            value: userCount || 0,
            change: '+8.1%',
            isPositive: true
          },
          {
            icon: <Building className="h-6 w-6 text-purple-400" />,
            label: 'Properties Listed',
            value: propertyCount || 0,
            change: '+5.4%',
            isPositive: true
          },
          {
            icon: <TrendingUp className="h-6 w-6 text-indigo-400" />,
            label: 'Transactions',
            value: transactionCount || 0,
            change: '+24.3%',
            isPositive: true
          }
        ]);

        setError(null);
      } catch (err) {
        console.error('Error fetching transaction stats:', err);
        setError('Failed to load transaction statistics');

        // Set fallback stats
        setStats([
          {
            icon: <DollarSign className="h-6 w-6 text-green-400" />,
            label: 'Total Invested',
            value: '$5,240,000',
            change: '+12.5%',
            isPositive: true
          },
          {
            icon: <Users className="h-6 w-6 text-blue-400" />,
            label: 'Active Users',
            value: 1250,
            change: '+8.1%',
            isPositive: true
          },
          {
            icon: <Building className="h-6 w-6 text-purple-400" />,
            label: 'Properties Listed',
            value: 48,
            change: '+5.4%',
            isPositive: true
          },
          {
            icon: <TrendingUp className="h-6 w-6 text-indigo-400" />,
            label: 'Transactions',
            value: 3240,
            change: '+24.3%',
            isPositive: true
          }
        ]);
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="w-full py-8 flex justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-5 w-5 text-indigo-500 animate-spin" />
          <span className="text-gray-500">Loading statistics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full py-4">
        <div className="text-center text-red-500 text-sm">{error}</div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
      {stats.map((stat, index) => (
        <div
          key={index}
          className="bg-gradient-to-br from-gray-900 to-indigo-950 border border-indigo-800/50 rounded-xl p-4 shadow-lg"
        >
          <div className="flex items-center mb-2">
            <div className="mr-3">
              {stat.icon}
            </div>
            <h3 className="text-gray-300 font-medium">{stat.label}</h3>
          </div>

          <div className="flex items-end justify-between">
            <div className="text-2xl font-bold text-white">{stat.value}</div>

            {stat.change && (
              <div className={`text-sm font-medium ${stat.isPositive ? 'text-green-400' : 'text-red-400'}`}>
                {stat.change}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
