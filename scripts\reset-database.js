#!/usr/bin/env node

/**
 * Reset Database Script
 * 
 * This script completely resets the database and rebuilds it with sample data
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Configuration - Use service role for full access
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function executeSQLFile() {
  console.log(colorize('\n🔄 Reading SQL file...', 'cyan'));
  
  try {
    const sqlPath = path.join(__dirname, 'reset-and-rebuild.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    console.log(colorize('✅ SQL file loaded successfully', 'green'));
    console.log(colorize(`📄 File size: ${sqlContent.length} characters`, 'blue'));
    
    console.log(colorize('\n🚀 Executing database reset...', 'yellow'));
    console.log(colorize('⚠️  This will drop all existing tables and data!', 'red'));
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });
    
    if (error) {
      console.error(colorize('❌ Error executing SQL:', 'red'), error.message);
      return false;
    }
    
    console.log(colorize('✅ Database reset completed successfully!', 'green'));
    
    // Show results if available
    if (data && data.length > 0) {
      console.log(colorize('\n📊 Results:', 'blue'));
      data.forEach(row => {
        if (row.result) {
          console.log(`   ${JSON.stringify(row.result)}`);
        }
      });
    }
    
    return true;
    
  } catch (error) {
    console.error(colorize('❌ Error reading or executing SQL file:', 'red'), error.message);
    return false;
  }
}

async function verifyReset() {
  console.log(colorize('\n🔍 Verifying database reset...', 'blue'));
  
  try {
    // Check users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('email, role, kyc_status');
    
    if (usersError) {
      console.log(colorize(`❌ Error checking users: ${usersError.message}`, 'red'));
    } else {
      console.log(colorize(`✅ Users: ${users?.length || 0}`, 'green'));
      if (users && users.length > 0) {
        users.forEach(user => {
          console.log(`   • ${user.email} (${user.role}) - KYC: ${user.kyc_status}`);
        });
      }
    }
    
    // Check properties
    const { data: properties, error: propertiesError } = await supabase
      .from('properties')
      .select('name, price, status');
    
    if (propertiesError) {
      console.log(colorize(`❌ Error checking properties: ${propertiesError.message}`, 'red'));
    } else {
      console.log(colorize(`✅ Properties: ${properties?.length || 0}`, 'green'));
      if (properties && properties.length > 0) {
        properties.forEach(property => {
          console.log(`   • ${property.name} - $${property.price?.toLocaleString()} (${property.status})`);
        });
      }
    }
    
    // Check investments
    const { data: investments, error: investmentsError } = await supabase
      .from('investments')
      .select('amount, shares, status');
    
    if (investmentsError) {
      console.log(colorize(`❌ Error checking investments: ${investmentsError.message}`, 'red'));
    } else {
      console.log(colorize(`✅ Investments: ${investments?.length || 0}`, 'green'));
      if (investments && investments.length > 0) {
        investments.forEach(investment => {
          console.log(`   • $${investment.amount?.toLocaleString()} (${investment.shares} shares) - ${investment.status}`);
        });
      }
    }
    
  } catch (error) {
    console.error(colorize('❌ Error verifying reset:', 'red'), error.message);
  }
}

async function main() {
  console.log(colorize('\n🔥 Database Reset and Rebuild', 'bright'));
  console.log(colorize('This will completely reset your local database with sample data', 'yellow'));
  console.log('─'.repeat(70));
  
  try {
    // Execute the SQL file
    const success = await executeSQLFile();
    
    if (success) {
      // Verify the results
      await verifyReset();
      
      console.log(colorize('\n🎉 Database reset completed successfully!', 'green'));
      console.log(colorize('\n🚀 Next Steps:', 'bright'));
      console.log('1. Check Supabase Studio: http://127.0.0.1:54323');
      console.log('2. View your Next.js app: http://localhost:3000');
      console.log('3. Test the marketplace and dashboard features');
      console.log('4. All sample data is now available without foreign key issues!');
      
      console.log(colorize('\n💡 What was created:', 'cyan'));
      console.log('• 3 sample users (Alice, Bob, Admin)');
      console.log('• 3 sample properties (apartment, house, condo)');
      console.log('• 3 sample investments');
      console.log('• All tables with proper relationships');
      console.log('• Indexes for performance');
      console.log('• Triggers for updated_at fields');
      
    } else {
      throw new Error('Failed to execute SQL file');
    }
    
  } catch (error) {
    console.error(colorize('\n❌ Reset failed:', 'red'), error.message);
    console.log(colorize('\n💡 Try running this manually in Supabase Studio SQL Editor:', 'yellow'));
    console.log('1. Open: http://127.0.0.1:54323');
    console.log('2. Go to SQL Editor');
    console.log('3. Copy and paste the contents of scripts/reset-and-rebuild.sql');
    console.log('4. Click Run');
    
    process.exit(1);
  }
}

main();
