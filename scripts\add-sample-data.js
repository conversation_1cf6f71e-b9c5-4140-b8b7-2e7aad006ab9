#!/usr/bin/env node

/**
 * Add Sample Data Script
 *
 * This script adds comprehensive sample data to your local Supabase database
 * for testing your Next.js app.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration - Use service role for bypassing RLS
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Sample data
const sampleUsers = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    wallet_address: '******************************************',
    email: '<EMAIL>',
    role: 'investor',
    kyc_status: 'verified'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    wallet_address: '0x8ba1f109551bD432803012645Hac136c30C6756',
    email: '<EMAIL>',
    role: 'property_owner',
    kyc_status: 'verified'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    wallet_address: '******************************************',
    email: '<EMAIL>',
    role: 'admin',
    kyc_status: 'verified'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    wallet_address: '******************************************',
    email: '<EMAIL>',
    role: 'investor',
    kyc_status: 'pending'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440005',
    wallet_address: '******************************************',
    email: '<EMAIL>',
    role: 'property_owner',
    kyc_status: 'verified'
  }
];

const sampleProperties = [
  {
    id: '660e8400-e29b-41d4-a716-446655440001',
    name: 'Luxury Downtown Apartment',
    description: 'A stunning 2-bedroom apartment in the heart of downtown with panoramic city views. Features modern amenities, hardwood floors, and a private balcony.',
    price: 500000,
    location: 'Downtown, City Center',
    property_type: 'apartment',
    bedrooms: 2,
    bathrooms: 2,
    square_feet: 1200,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440002',
    images: [
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
      'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800'
    ],
    metadata: {
      year_built: 2018,
      parking_spaces: 1,
      amenities: ['gym', 'pool', 'concierge'],
      pet_friendly: true
    }
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440002',
    name: 'Suburban Family Home',
    description: 'Perfect family home with large backyard, updated kitchen, and excellent school district. Move-in ready with recent renovations.',
    price: 750000,
    location: 'Suburban Heights',
    property_type: 'house',
    bedrooms: 4,
    bathrooms: 3,
    square_feet: 2500,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440002',
    images: [
      'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800',
      'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800'
    ],
    metadata: {
      year_built: 2010,
      parking_spaces: 2,
      amenities: ['garden', 'garage', 'fireplace'],
      pet_friendly: true
    }
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440003',
    name: 'Modern Condo with City View',
    description: 'Stunning modern condo with panoramic city views, floor-to-ceiling windows, and premium finishes throughout.',
    price: 650000,
    location: 'Midtown District',
    property_type: 'condo',
    bedrooms: 3,
    bathrooms: 2,
    square_feet: 1800,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440005',
    images: [
      'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800',
      'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800'
    ],
    metadata: {
      year_built: 2020,
      parking_spaces: 1,
      amenities: ['rooftop_deck', 'gym', 'doorman'],
      pet_friendly: false
    }
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440004',
    name: 'Cozy Studio Loft',
    description: 'Charming studio loft in trendy arts district. High ceilings, exposed brick, and walking distance to galleries and cafes.',
    price: 350000,
    location: 'Arts District',
    property_type: 'studio',
    bedrooms: 1,
    bathrooms: 1,
    square_feet: 800,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440005',
    images: [
      'https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800'
    ],
    metadata: {
      year_built: 1995,
      parking_spaces: 0,
      amenities: ['exposed_brick', 'high_ceilings'],
      pet_friendly: true
    }
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440005',
    name: 'Waterfront Penthouse',
    description: 'Exclusive penthouse with private terrace and stunning waterfront views. The epitome of luxury living.',
    price: 1200000,
    location: 'Waterfront District',
    property_type: 'penthouse',
    bedrooms: 4,
    bathrooms: 4,
    square_feet: 3500,
    status: 'draft',
    owner_id: '550e8400-e29b-41d4-a716-446655440002',
    images: [
      'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800',
      'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800'
    ],
    metadata: {
      year_built: 2022,
      parking_spaces: 3,
      amenities: ['private_terrace', 'wine_cellar', 'smart_home'],
      pet_friendly: true
    }
  }
];

const sampleInvestments = [
  {
    id: '770e8400-e29b-41d4-a716-446655440001',
    property_id: '660e8400-e29b-41d4-a716-446655440001',
    investor_id: '550e8400-e29b-41d4-a716-446655440001',
    amount: 50000,
    shares: 10,
    transaction_hash: '******************************************901234567890abcdef1234567890',
    status: 'completed'
  },
  {
    id: '770e8400-e29b-41d4-a716-446655440002',
    property_id: '660e8400-e29b-41d4-a716-446655440002',
    investor_id: '550e8400-e29b-41d4-a716-446655440001',
    amount: 75000,
    shares: 10,
    transaction_hash: '******************************************34567890abcdef1234567890ab',
    status: 'completed'
  },
  {
    id: '770e8400-e29b-41d4-a716-446655440003',
    property_id: '660e8400-e29b-41d4-a716-446655440003',
    investor_id: '550e8400-e29b-41d4-a716-446655440004',
    amount: 32500,
    shares: 5,
    transaction_hash: '0xfedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321',
    status: 'pending'
  },
  {
    id: '770e8400-e29b-41d4-a716-446655440004',
    property_id: '660e8400-e29b-41d4-a716-446655440001',
    investor_id: '550e8400-e29b-41d4-a716-446655440004',
    amount: 25000,
    shares: 5,
    transaction_hash: null,
    status: 'pending'
  }
];

async function clearExistingData() {
  console.log(colorize('🧹 Clearing existing data...', 'yellow'));

  try {
    // Clear in reverse order of dependencies
    await supabase.from('investments').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('properties').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    console.log(colorize('✅ Existing data cleared', 'green'));
  } catch (error) {
    console.error(colorize('❌ Error clearing data:', 'red'), error.message);
  }
}

async function addUsers() {
  console.log(colorize('👥 Adding sample users...', 'blue'));

  try {
    const { data, error } = await supabase
      .from('users')
      .insert(sampleUsers)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Added ${data.length} users`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error adding users:', 'red'), error.message);
    return [];
  }
}

async function addProperties() {
  console.log(colorize('🏠 Adding sample properties...', 'blue'));

  try {
    const { data, error } = await supabase
      .from('properties')
      .insert(sampleProperties)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Added ${data.length} properties`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error adding properties:', 'red'), error.message);
    return [];
  }
}

async function addInvestments() {
  console.log(colorize('💰 Adding sample investments...', 'blue'));

  try {
    const { data, error } = await supabase
      .from('investments')
      .insert(sampleInvestments)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Added ${data.length} investments`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error adding investments:', 'red'), error.message);
    return [];
  }
}

async function showSummary() {
  console.log(colorize('\n📊 Database Summary:', 'bright'));

  try {
    // Get counts
    const { data: userCount } = await supabase.rpc('exec_sql', {
      sql: 'SELECT COUNT(*) as count FROM users'
    });
    const { data: propertyCount } = await supabase.rpc('exec_sql', {
      sql: 'SELECT COUNT(*) as count FROM properties'
    });
    const { data: investmentCount } = await supabase.rpc('exec_sql', {
      sql: 'SELECT COUNT(*) as count FROM investments'
    });

    console.log(`   👥 Users: ${userCount[0]?.result?.count || 0}`);
    console.log(`   🏠 Properties: ${propertyCount[0]?.result?.count || 0}`);
    console.log(`   💰 Investments: ${investmentCount[0]?.result?.count || 0}`);

  } catch (error) {
    console.error(colorize('❌ Error getting summary:', 'red'), error.message);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const shouldClear = args.includes('--clear') || args.includes('--reset');

  console.log(colorize('\n🌱 Adding Sample Data to Local Supabase', 'bright'));
  console.log('─'.repeat(50));

  try {
    if (shouldClear) {
      await clearExistingData();
    }

    const users = await addUsers();
    const properties = await addProperties();
    const investments = await addInvestments();

    console.log(colorize('\n🎉 Sample data added successfully!', 'green'));
    await showSummary();

    console.log(colorize('\n🚀 Next Steps:', 'bright'));
    console.log('1. Start your Next.js app: npm run dev');
    console.log('2. Visit: http://localhost:3000');
    console.log('3. Check the marketplace, dashboard, and other pages');
    console.log('4. View data in Studio: http://127.0.0.1:54323');

  } catch (error) {
    console.error(colorize('\n❌ Failed to add sample data:', 'red'), error.message);
    process.exit(1);
  }
}

main();
