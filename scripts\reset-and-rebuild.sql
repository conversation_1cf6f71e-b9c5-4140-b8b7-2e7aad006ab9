-- Reset and Rebuild Database Schema
-- This script drops everything and rebuilds from scratch with sample data

-- Step 1: Drop everything (tables, functions, triggers, policies)
DROP TABLE IF EXISTS public.investments CASCADE;
DROP TABLE IF EXISTS public.properties CASCADE;
DROP TABLE IF EXISTS public.kyc_documents CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS user_role_type CASCADE;
DROP TYPE IF EXISTS kyc_status_type CASCADE;
DROP TYPE IF EXISTS property_status_type CASCADE;
DROP TYPE IF EXISTS investment_status_type CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS exec_sql(TEXT) CASCADE;

-- Step 2: Create custom types
CREATE TYPE user_role_type AS ENUM ('investor', 'property_owner', 'admin');
CREATE TYPE kyc_status_type AS ENUM ('none', 'pending', 'verified', 'rejected');
CREATE TYPE property_status_type AS ENUM ('draft', 'active', 'sold', 'inactive');
CREATE TYPE investment_status_type AS ENUM ('pending', 'completed', 'failed', 'refunded');

-- Step 3: Create users table (WITHOUT foreign key constraint initially)
CREATE TABLE public.users (
  id UUID PRIMARY KEY,
  wallet_address TEXT UNIQUE,
  email TEXT,
  role user_role_type,
  kyc_status kyc_status_type DEFAULT 'none',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 4: Insert sample users FIRST
INSERT INTO public.users (id, email, wallet_address, role, kyc_status, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '******************************************', 'investor', 'verified', now(), now()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '0x8ba1f109551bD432803012645Hac136c30C6756', 'property_owner', 'verified', now(), now()),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '******************************************', 'admin', 'verified', now(), now());

-- Step 5: Create properties table
CREATE TABLE public.properties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(15,2) NOT NULL,
  location TEXT NOT NULL,
  property_type TEXT NOT NULL,
  bedrooms INTEGER,
  bathrooms INTEGER,
  square_feet INTEGER,
  status property_status_type DEFAULT 'draft',
  owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  images TEXT[], -- Array of image URLs
  documents TEXT[], -- Array of document URLs
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 6: Insert sample properties
INSERT INTO public.properties (id, name, description, price, location, property_type, bedrooms, bathrooms, square_feet, status, owner_id, images, metadata, created_at, updated_at) VALUES
('660e8400-e29b-41d4-a716-************', 'Luxury Downtown Apartment', 'A stunning 2-bedroom apartment in the heart of downtown with panoramic city views. Features modern amenities, hardwood floors, and a private balcony.', 500000, 'Downtown, City Center', 'apartment', 2, 2, 1200, 'active', '550e8400-e29b-41d4-a716-************', ARRAY['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800'], '{"year_built": 2018, "parking_spaces": 1, "amenities": ["gym", "pool", "concierge"], "pet_friendly": true}'::jsonb, now(), now()),
('660e8400-e29b-41d4-a716-************', 'Suburban Family Home', 'Perfect family home with large backyard and excellent school district. Move-in ready with recent renovations.', 750000, 'Suburban Heights', 'house', 4, 3, 2500, 'active', '550e8400-e29b-41d4-a716-************', ARRAY['https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800'], '{"year_built": 2010, "parking_spaces": 2, "amenities": ["garden", "garage", "fireplace"], "pet_friendly": true}'::jsonb, now(), now()),
('660e8400-e29b-41d4-a716-************', 'Modern Condo with City View', 'Stunning modern condo with panoramic city views and premium finishes throughout.', 650000, 'Midtown District', 'condo', 3, 2, 1800, 'active', '550e8400-e29b-41d4-a716-************', ARRAY['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800'], '{"year_built": 2020, "parking_spaces": 1, "amenities": ["rooftop_deck", "gym", "doorman"], "pet_friendly": false}'::jsonb, now(), now());

-- Step 7: Create investments table
CREATE TABLE public.investments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  investor_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  amount DECIMAL(15,2) NOT NULL,
  shares INTEGER NOT NULL DEFAULT 1,
  transaction_hash TEXT,
  status investment_status_type DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 8: Insert sample investments
INSERT INTO public.investments (id, property_id, investor_id, amount, shares, transaction_hash, status, created_at, updated_at) VALUES
('770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 50000, 10, '******************************************901234567890abcdef1234567890', 'completed', now(), now()),
('770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 75000, 10, '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab', 'completed', now(), now()),
('770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 65000, 10, '0xfedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321', 'completed', now(), now());

-- Step 9: Create KYC documents table
CREATE TABLE kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL,
  document_category TEXT NOT NULL, -- 'identity', 'address', 'financial'
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status kyc_status_type DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES public.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 10: Create utility functions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 11: Create triggers for updated_at
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_properties_updated_at
BEFORE UPDATE ON public.properties
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_investments_updated_at
BEFORE UPDATE ON public.investments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kyc_documents_updated_at
BEFORE UPDATE ON kyc_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Step 12: Create indexes for performance
CREATE INDEX users_wallet_address_idx ON public.users (wallet_address);
CREATE INDEX users_role_idx ON public.users (role);
CREATE INDEX users_kyc_status_idx ON public.users (kyc_status);
CREATE INDEX properties_owner_id_idx ON public.properties (owner_id);
CREATE INDEX properties_status_idx ON public.properties (status);
CREATE INDEX investments_property_id_idx ON public.investments (property_id);
CREATE INDEX investments_investor_id_idx ON public.investments (investor_id);
CREATE INDEX investments_status_idx ON public.investments (status);
CREATE INDEX kyc_documents_user_id_idx ON kyc_documents (user_id);

-- Step 13: Show results
SELECT 'Database Reset Complete!' as status;
SELECT 'Users' as table_name, COUNT(*) as count FROM public.users
UNION ALL
SELECT 'Properties' as table_name, COUNT(*) as count FROM public.properties
UNION ALL
SELECT 'Investments' as table_name, COUNT(*) as count FROM public.investments
UNION ALL
SELECT 'KYC Documents' as table_name, COUNT(*) as count FROM kyc_documents;
