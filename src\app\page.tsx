'use client';
import React, { useEffect } from 'react';
import Link from 'next/link';
import {
  ArrowR<PERSON>, ChevronRight, Building, Users, Wallet,
  BarChart3, Globe, Lock, Loader2,
  Coins, Shield
} from 'lucide-react';
import { useWalletAuth } from '@/context/WalletAuthContext';
import WalletAuthButton from '@/components/WalletAuthButton';
import TransactionStats from '@/components/TransactionStats';

// Hero section with animated elements
const Hero = () => {
  const { isWalletConnected, loading } = useWalletAuth();

  return (
    <div className="relative min-h-screen flex items-center">
      {/* Background with overlay */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/90 to-purple-900/90 z-10"></div>
        <div className="absolute inset-0 bg-[url('/assets/House2.jpg')] bg-cover bg-center z-0"></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-6 relative z-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="text-white">
            <h1 className="text-5xl md:text-7xl font-bold leading-tight mb-6">
              <span className="block">Invest in</span>
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">Tokenized</span>
              <span className="block">Real Estate</span>
            </h1>
            <p className="text-xl text-gray-200 mb-8 max-w-lg">
              Connect your wallet to access global properties on the blockchain. Own premium real estate with as little as $100.
            </p>

            {loading ? (
              <div className="flex justify-center">
                <Loader2 className="h-12 w-12 text-indigo-500 animate-spin" />
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4">
                <WalletAuthButton size="lg" />

                {!isWalletConnected && (
                  <Link
                    href="/how-it-works"
                    className="px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white font-semibold hover:bg-white/20 transition-all flex items-center justify-center"
                  >
                    Learn More <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                )}
              </div>
            )}
          </div>

          <div className="hidden lg:block">
            <div className="relative">
              {/* Animated floating cards */}
              <div className="absolute top-0 right-0 w-64 h-40 bg-white/10 backdrop-blur-md rounded-2xl p-4 shadow-xl border border-white/20 transform -translate-y-20 animate-float-slow">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-indigo-500 flex items-center justify-center">
                    <Building className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold">Property #1432</h3>
                    <p className="text-xs text-gray-300">Luxury Villa</p>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Value</span>
                  <span className="text-white font-bold">2.4 ETH</span>
                </div>
                <div className="mt-1 bg-gray-700 h-1.5 rounded-full">
                  <div className="bg-gradient-to-r from-purple-500 to-indigo-500 h-1.5 rounded-full w-3/4"></div>
                </div>
                <div className="flex justify-between text-xs mt-1">
                  <span className="text-gray-400">75% Funded</span>
                  <span className="text-gray-400">156 Investors</span>
                </div>
              </div>

              <div className="absolute bottom-0 left-0 w-60 h-40 bg-white/10 backdrop-blur-md rounded-2xl p-4 shadow-xl border border-white/20 transform translate-x-10 translate-y-20 animate-float">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center">
                    <Wallet className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold">Your Wallet</h3>
                    <p className="text-xs text-gray-300">Connection Status</p>
                  </div>
                </div>
                <div className="flex justify-between mt-2">
                  <div>
                    <p className="text-gray-400 text-xs">Balance</p>
                    <p className="text-white font-mono font-medium">5.2 ETH</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-xs">Properties</p>
                    <p className="text-white font-medium">7</p>
                  </div>
                </div>
                <button className="mt-2 w-full py-1.5 bg-white/10 hover:bg-white/20 transition-colors rounded text-white text-sm">
                  Connect
                </button>
              </div>

              {/* Main image */}
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-1.5 shadow-2xl shadow-purple-500/30 transform translate-y-4 rotate-2">
                <img
                  src="/assets/House3.jpg"
                  alt="Luxury real estate"
                  className="rounded-2xl w-full h-96 object-cover"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Stats section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
            <p className="text-4xl font-bold text-white mb-1">$613T+</p>
            <p className="text-gray-300">Global Real Estate Market</p>
          </div>
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
            <p className="text-4xl font-bold text-white mb-1">1,000</p>
            <p className="text-gray-300">Shares Per Property</p>
          </div>
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
            <p className="text-4xl font-bold text-white mb-1">0.65%</p>
            <p className="text-gray-300">BCT Discounted Fee</p>
          </div>
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
            <p className="text-4xl font-bold text-white mb-1">24/7</p>
            <p className="text-gray-300">Global Investment Access</p>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-white animate-bounce">
        <p className="text-sm mb-2">Scroll Down</p>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 5L12 19M12 19L19 12M12 19L5 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>
    </div>
  );
};

// How It Works section
const HowItWorks = () => {
  return (
    <div className="bg-gradient-to-b from-gray-900 to-indigo-950 py-24">
      <div className="container mx-auto px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">How BrickChain Works</h2>
          <p className="text-xl text-gray-300">Our blockchain technology makes real estate investment simple, secure, and accessible to everyone</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-gradient-to-br from-indigo-800/50 to-purple-800/50 backdrop-blur-sm rounded-2xl p-8 border border-indigo-500/20">
            <div className="bg-indigo-600 w-12 h-12 rounded-full flex items-center justify-center mb-6">
              <Wallet className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Connect Your Wallet</h3>
            <p className="text-gray-300 mb-6">Start by connecting your crypto wallet to our platform. We support MetaMask, WalletConnect, and other popular providers.</p>
            <div className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors">
              <Link href="/how-it-works" className="flex items-center">
                <span>Learn more</span>
                <ChevronRight className="h-5 w-5 ml-1" />
              </Link>
            </div>
          </div>

          <div className="bg-gradient-to-br from-indigo-800/50 to-purple-800/50 backdrop-blur-sm rounded-2xl p-8 border border-indigo-500/20 md:transform md:translate-y-8">
            <div className="bg-indigo-600 w-12 h-12 rounded-full flex items-center justify-center mb-6">
              <Building className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Browse Properties</h3>
            <p className="text-gray-300 mb-6">Explore our marketplace of tokenized properties. Each property is divided into 1,000 shares for fractional ownership.</p>
            <div className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors">
              <Link href="/marketplace" className="flex items-center">
                <span>View marketplace</span>
                <ChevronRight className="h-5 w-5 ml-1" />
              </Link>
            </div>
          </div>

          <div className="bg-gradient-to-br from-indigo-800/50 to-purple-800/50 backdrop-blur-sm rounded-2xl p-8 border border-indigo-500/20">
            <div className="bg-indigo-600 w-12 h-12 rounded-full flex items-center justify-center mb-6">
              <Coins className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Invest & Earn</h3>
            <p className="text-gray-300 mb-6">Purchase property tokens with cryptocurrency. Receive rental income and property appreciation directly to your wallet.</p>
            <div className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors">
              <Link href="/how-it-works" className="flex items-center">
                <span>Learn more</span>
                <ChevronRight className="h-5 w-5 ml-1" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Wallet Authentication section
const WalletAuthentication = () => {
  return (
    <div className="bg-gradient-to-b from-indigo-950 to-gray-900 py-24">
      <div className="container mx-auto px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Wallet-Based Authentication</h2>
          <p className="text-xl text-gray-300">Secure, seamless access with your crypto wallet. No passwords to remember or reset.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-indigo-900/20 backdrop-blur-sm border border-indigo-800/30 rounded-xl p-6">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
              <Wallet className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Connect Your Wallet</h3>
            <p className="text-gray-300">
              Simply connect your preferred crypto wallet to access all platform features. No username or password required.
            </p>
          </div>

          <div className="bg-indigo-900/20 backdrop-blur-sm border border-indigo-800/30 rounded-xl p-6">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Cryptographic Security</h3>
            <p className="text-gray-300">
              Military-grade security with blockchain verification for all transactions and authentications.
            </p>
          </div>

          <div className="bg-indigo-900/20 backdrop-blur-sm border border-indigo-800/30 rounded-xl p-6">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center mb-4">
              <Users className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Unified Experience</h3>
            <p className="text-gray-300">
              One wallet address for all your property investments and listings. Seamless role switching between investor and property owner.
            </p>
          </div>
        </div>

        <div className="mt-12 flex justify-center">
          <WalletAuthButton size="lg" />
        </div>
      </div>
    </div>
  );
};

// Benefits section
const Benefits = () => {
  return (
    <div className="bg-gradient-to-b from-gray-900 to-indigo-950 py-24">
      <div className="container mx-auto px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Why Choose BrickChain</h2>
          <p className="text-xl text-gray-300">We're transforming real estate investing with blockchain technology, providing unique advantages over traditional methods</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            { icon: <Wallet className="h-6 w-6" />, title: "Low Entry Barrier", desc: "Start investing with as little as $100, no need for large capital or mortgage approvals" },
            { icon: <Globe className="h-6 w-6" />, title: "Global Access", desc: "Invest in international real estate markets without legal complications or travel" },
            { icon: <BarChart3 className="h-6 w-6" />, title: "Instant Liquidity", desc: "Trade your property tokens anytime through our marketplace, unlike traditional illiquid real estate" },
            { icon: <Lock className="h-6 w-6" />, title: "Transaction Security", desc: "Blockchain technology ensures transparent, secure, and immutable transaction records" },
            { icon: <Users className="h-6 w-6" />, title: "Community Governance", desc: "Participate in property decisions through our democratic governance system" },
            { icon: <Coins className="h-6 w-6" />, title: "Automated Income", desc: "Receive rental income and appreciation directly to your wallet without intermediaries" }
          ].map((benefit, index) => (
            <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-6 hover:bg-white/10 transition-colors border border-white/5">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white mb-4">
                {benefit.icon}
              </div>
              <h3 className="text-xl font-bold text-white mb-2">{benefit.title}</h3>
              <p className="text-gray-400">{benefit.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Featured Properties section
const FeaturedProperties = () => {
  return (
    <div className="bg-gradient-to-b from-indigo-950 to-gray-900 py-24">
      <div className="container mx-auto px-6">
        <div className="flex flex-col md:flex-row justify-between items-center mb-16">
          <div>
            <h2 className="text-4xl font-bold text-white mb-2">Featured Properties</h2>
            <p className="text-xl text-gray-300">Explore our curated selection of premium real estate opportunities</p>
          </div>
          <Link
            href="/marketplace"
            className="mt-6 md:mt-0 px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-full text-white font-medium flex items-center transition-colors"
          >
            View All Properties <ChevronRight className="h-5 w-5 ml-1" />
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            { id: 1, name: "Downtown Duplex", location: "Accra, Ghana", price: "100 ETH", image: "/assets/House6.jpg", funding: 65 },
            { id: 2, name: "Beachfront Villa", location: "Cape Town, South Africa", price: "150 ETH", image: "/assets/House7.jpg", funding: 78 },
            { id: 3, name: "Urban Apartment", location: "Lagos, Nigeria", price: "80 ETH", image: "/assets/House8.jpg", funding: 42 }
          ].map((property) => (
            <div key={property.id} className="bg-gradient-to-br from-indigo-800/30 to-purple-800/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-500/20 group hover:shadow-xl hover:shadow-purple-600/10 transition-all duration-300">
              <div className="relative h-64 overflow-hidden">
                <img
                  src={property.image}
                  alt={property.name}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-2xl font-bold text-white">{property.name}</h3>
                  <p className="text-gray-300">{property.location}</p>
                </div>
              </div>
              <div className="p-6">
                <div className="flex justify-between mb-2">
                  <div>
                    <span className="text-gray-400 text-sm">Price</span>
                    <p className="text-white font-bold">{property.price}</p>
                  </div>
                  <div>
                    <span className="text-gray-400 text-sm">Yield</span>
                    <p className="text-green-400 font-bold">8.2% <span className="text-sm">est.</span></p>
                  </div>
                  <div>
                    <span className="text-gray-400 text-sm">Shares</span>
                    <p className="text-white font-bold">1,000</p>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-300">{property.funding}% Funded</span>
                    <span className="text-sm text-gray-300">{property.funding * 10} Investors</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full"
                      style={{ width: `${property.funding}%` }}
                    ></div>
                  </div>
                </div>
                <button className="mt-6 w-full py-3 bg-white/10 hover:bg-white/20 rounded-lg text-white font-medium transition-colors">
                  View Property
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Call to Action section
const CallToAction = () => {
  const { isWalletConnected } = useWalletAuth();

  return (
    <div className="bg-gradient-to-r from-indigo-900 to-purple-900 py-20">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-4xl font-bold text-white mb-6">Ready to Start Your Real Estate Investment Journey?</h2>
        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
          Join thousands of investors who are already diversifying their portfolios with tokenized real estate on BrickChain.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <div className="inline-block">
            <WalletAuthButton size="lg" />
          </div>
          {isWalletConnected && (
            <Link
              href="/marketplace"
              className="px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white font-semibold hover:bg-white/20 transition-all flex items-center justify-center"
            >
              Browse Properties <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          )}
        </div>

        {/* Debug link - only visible in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 inline-block">
            <Link
              href="/debug"
              className="text-gray-400 hover:text-white text-sm underline transition-colors"
            >
              Debug Dashboard
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

// Animation for floating elements
const floatAnimation = `
@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(5px, -5px) rotate(1deg); }
  50% { transform: translate(0, -10px) rotate(0deg); }
  75% { transform: translate(-5px, -5px) rotate(-1deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

@keyframes float-slow {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-5px, 10px) rotate(-1deg); }
  50% { transform: translate(0, 15px) rotate(0deg); }
  75% { transform: translate(5px, 10px) rotate(1deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}
`;

// Main Home component
export default function Home() {
  // Add the animation styles
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = floatAnimation;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <>
      <Hero />
      <HowItWorks />
      <Benefits />
      <FeaturedProperties />

      {/* Transaction Statistics Section */}
      <div className="bg-gradient-to-b from-indigo-950 to-gray-900 py-24">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Platform Statistics</h2>
            <p className="text-xl text-gray-300">Real-time insights into BrickChain's growing ecosystem</p>
          </div>

          <TransactionStats />
        </div>
      </div>

      <WalletAuthentication />
      <CallToAction />
    </>
  );
}