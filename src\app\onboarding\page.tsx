'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useWalletAuth } from '@/context/WalletAuthContext';
import WalletAuthButton from '@/components/WalletAuthButton';
import { Wallet, Shield, User, CheckCircle, ArrowRight } from 'lucide-react';

export default function OnboardingPage() {
  const router = useRouter();
  const { isAuthenticated, isProfileComplete, loading, isWalletConnected } = useWalletAuth();

  // Redirect authenticated users with complete profiles
  useEffect(() => {
    if (!loading && isAuthenticated && isProfileComplete) {
      router.push('/dashboard');
    }
  }, [loading, isAuthenticated, isProfileComplete, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  const currentStep = !isWalletConnected ? 0 : !isAuthenticated ? 1 : !isProfileComplete ? 2 : 3;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 py-12">
      <div className="max-w-2xl mx-auto p-6 text-center">
        <h1 className="text-4xl font-bold text-white mb-4">Welcome to BrickChain</h1>
        <p className="text-xl text-gray-300 mb-12">Get started in 3 simple steps</p>

        {/* Progress Steps */}
        <div className="flex justify-center mb-12">
          {['Connect', 'Authenticate', 'Complete Profile'].map((step, index) => (
            <div key={index} className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                index <= currentStep ? 'bg-indigo-600 text-white' : 'bg-gray-700 text-gray-400'
              }`}>
                {index < currentStep ? <CheckCircle size={20} /> : index + 1}
              </div>
              {index < 2 && <div className={`w-16 h-0.5 mx-4 ${index < currentStep ? 'bg-indigo-600' : 'bg-gray-700'}`} />}
            </div>
          ))}
        </div>

        {/* Current Step Content */}
        <div className="bg-gray-800/50 rounded-xl p-8 border border-gray-700">
          {currentStep === 0 && (
            <>
              <Wallet size={48} className="text-indigo-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-4">Connect Your Wallet</h2>
              <p className="text-gray-300 mb-8">Connect your Web3 wallet to get started</p>
              <WalletAuthButton size="lg" />
            </>
          )}

          {currentStep === 1 && (
            <>
              <Shield size={48} className="text-indigo-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-4">Verify Your Identity</h2>
              <p className="text-gray-300 mb-8">Sign a message to verify wallet ownership</p>
              <WalletAuthButton size="lg" />
            </>
          )}

          {currentStep === 2 && (
            <>
              <User size={48} className="text-indigo-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-4">Complete Your Profile</h2>
              <p className="text-gray-300 mb-8">Add your personal information to start investing</p>
              <button
                onClick={() => router.push('/complete-profile')}
                className="px-8 py-4 bg-indigo-600 hover:bg-indigo-700 rounded-full text-white font-semibold flex items-center mx-auto"
              >
                Complete Profile <ArrowRight className="ml-2 h-5 w-5" />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
