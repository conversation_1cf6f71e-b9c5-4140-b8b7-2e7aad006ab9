'use client';

import { ConnectButton } from '@rainbow-me/rainbowkit';
import { Wallet, ExternalLink } from 'lucide-react';
import { getBlockExplorerUrl, formatAddress } from '@/utils/web3';
import Button from '@/components/ui/Button';

interface ConnectWalletButtonProps {
  variant?: 'default' | 'minimal' | 'sidebar';
  showBalance?: boolean;
}

/**
 * ConnectWalletButton is a customized implementation of RainbowKit's ConnectButton
 * It provides a consistent wallet connection experience across the application with
 * several variants for different UI contexts.
 *
 * This component uses the centralized WalletContext to avoid duplicate wallet connections.
 */
export default function ConnectWalletButton({
  variant = 'default',
  showBalance = true
}: ConnectWalletButtonProps) {

  return (
    <ConnectButton.Custom>
      {({
        account,
        chain,
        openAccountModal,
        openChainModal,
        openConnectModal,
        authenticationStatus,
        mounted,
      }) => {
        const connected = mounted &&
          account &&
          chain &&
          (!authenticationStatus || authenticationStatus === 'authenticated');

        if (variant === 'minimal') {
          return (
            <div className="flex items-center">
              {(() => {
                if (!connected) {
                  return (
                    <Button
                      onClick={() => {
                        openConnectModal();
                        // Note: We can't directly detect connection success here
                        // The success toast will be handled by the wallet context
                      }}
                      variant="ghost"
                      size="sm"
                    >
                      Connect Wallet
                    </Button>
                  );
                }

                return (
                  <Button
                    onClick={openAccountModal}
                    variant="ghost"
                    size="sm"
                  >
                    {account.displayName}
                  </Button>
                );
              })()}
            </div>
          );
        }

        if (variant === 'sidebar') {
          return (
            <div className="w-full">
              {(() => {
                if (!connected) {
                  return (
                    <Button
                      onClick={openConnectModal}
                      variant="primary"
                      size="lg"
                      fullWidth
                      rounded="full"
                      icon={<Wallet size={18} />}
                    >
                      Connect Wallet
                    </Button>
                  );
                }

                return (
                  <div className="bg-gray-800/50 p-4 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 to-indigo-500 rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium">Connected</p>
                          <p className="text-xs text-gray-400">
                            {formatAddress(account.address)}
                          </p>
                        </div>
                      </div>
                      <button
                        className="text-xs text-red-400"
                        onClick={openAccountModal}
                      >
                        Disconnect
                      </button>
                    </div>
                    {showBalance && account.balanceFormatted && (
                      <div className="mt-3 border-t border-gray-700 pt-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Balance</span>
                          <span>{account.balanceFormatted}</span>
                        </div>
                      </div>
                    )}
                    <div className="mt-2 flex justify-between gap-2">
                      <button
                        onClick={openChainModal}
                        className="flex-1 text-xs px-2 py-1.5 bg-gray-700 hover:bg-gray-600 rounded text-center"
                      >
                        {chain.name}
                      </button>
                      <a
                        href={getBlockExplorerUrl(chain.id, account.address)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 text-xs px-2 py-1.5 bg-gray-700 hover:bg-gray-600 rounded flex items-center justify-center gap-1"
                      >
                        <ExternalLink size={12} />
                        Explorer
                      </a>
                    </div>
                  </div>
                );
              })()}
            </div>
          );
        }

        // Default variant
        return (
          <div className="flex items-center gap-3">
            {chain && connected && (
              <button
                onClick={openChainModal}
                className="hidden md:flex items-center gap-1 px-3 py-1.5 bg-indigo-900/50 border border-indigo-700/30 rounded-lg text-indigo-300 hover:bg-indigo-800/50 transition-colors"
                type="button"
              >
                {chain.hasIcon && (
                  <div className="w-5 h-5 rounded-full overflow-hidden">
                    {chain.iconUrl && (
                      <img
                        alt={chain.name ?? 'Chain icon'}
                        src={chain.iconUrl}
                        className="w-full h-full"
                      />
                    )}
                  </div>
                )}
                <span className="text-sm font-medium">{chain.name}</span>
              </button>
            )}

            {(() => {
              if (!connected) {
                return (
                  <Button
                    onClick={openConnectModal}
                    variant="primary"
                    size="md"
                    icon={<Wallet size={16} />}
                  >
                    Connect Wallet
                  </Button>
                );
              }

              return (
                <Button
                  onClick={openAccountModal}
                  variant="secondary"
                  size="md"
                  icon={<Wallet size={16} />}
                >
                  <span>{account.displayName}</span>
                  {showBalance && account.balanceFormatted && (
                    <span className="hidden md:inline">
                      ({account.balanceFormatted})
                    </span>
                  )}
                </Button>
              );
            })()}
          </div>
        );
      }}
    </ConnectButton.Custom>
  );
}