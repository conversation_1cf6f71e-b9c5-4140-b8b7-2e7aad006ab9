# 📊 Adding Sample Data to Your Local Supabase

Since the automated scripts require auth users, the easiest way to add sample data for testing is through Supabase Studio.

## 🌐 Access Supabase Studio

1. **Open Supabase Studio**: http://127.0.0.1:54323
2. **Navigate to Table Editor** (left sidebar)

## 📝 Step-by-Step Data Addition

### Step 1: Add Sample Users

1. **Go to the `users` table**
2. **Click "Insert" → "Insert row"**
3. **Add these sample users one by one:**

**User 1 - Investor:**
```
id: 550e8400-e29b-41d4-a716-************
wallet_address: ******************************************
email: <EMAIL>
role: investor
kyc_status: verified
```

**User 2 - Property Owner:**
```
id: 550e8400-e29b-41d4-a716-446655440002
wallet_address: 0x8ba1f109551bD432803012645Hac136c30C6756
email: <EMAIL>
role: property_owner
kyc_status: verified
```

**User 3 - Admin:**
```
id: 550e8400-e29b-41d4-a716-446655440003
wallet_address: ******************************************
email: <EMAIL>
role: admin
kyc_status: verified
```

### Step 2: Add Sample Properties

1. **Go to the `properties` table**
2. **Click "Insert" → "Insert row"**
3. **Add these sample properties:**

**Property 1 - Downtown Apartment:**
```
name: Luxury Downtown Apartment
description: A stunning 2-bedroom apartment in the heart of downtown with panoramic city views.
price: 500000
location: Downtown, City Center
property_type: apartment
bedrooms: 2
bathrooms: 2
square_feet: 1200
status: active
owner_id: 550e8400-e29b-41d4-a716-446655440002
images: ["https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800"]
metadata: {"year_built": 2018, "parking_spaces": 1, "amenities": ["gym", "pool", "concierge"]}
```

**Property 2 - Suburban Home:**
```
name: Suburban Family Home
description: Perfect family home with large backyard and excellent school district.
price: 750000
location: Suburban Heights
property_type: house
bedrooms: 4
bathrooms: 3
square_feet: 2500
status: active
owner_id: 550e8400-e29b-41d4-a716-446655440002
images: ["https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800"]
metadata: {"year_built": 2010, "parking_spaces": 2, "amenities": ["garden", "garage", "fireplace"]}
```

**Property 3 - Modern Condo:**
```
name: Modern Condo with City View
description: Stunning modern condo with panoramic city views and premium finishes.
price: 650000
location: Midtown District
property_type: condo
bedrooms: 3
bathrooms: 2
square_feet: 1800
status: active
owner_id: 550e8400-e29b-41d4-a716-446655440002
images: ["https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800"]
metadata: {"year_built": 2020, "parking_spaces": 1, "amenities": ["rooftop_deck", "gym", "doorman"]}
```

### Step 3: Add Sample Investments

1. **Go to the `investments` table**
2. **Click "Insert" → "Insert row"**
3. **Add these sample investments:**

**Investment 1:**
```
property_id: [Select from dropdown - Downtown Apartment]
investor_id: 550e8400-e29b-41d4-a716-************
amount: 50000
shares: 10
transaction_hash: ******************************************901234567890abcdef1234567890
status: completed
```

**Investment 2:**
```
property_id: [Select from dropdown - Suburban Home]
investor_id: 550e8400-e29b-41d4-a716-************
amount: 75000
shares: 10
transaction_hash: 0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab
status: completed
```

## 🎯 Quick Tips for Studio

### Adding JSON Data:
- For `images` array: `["url1", "url2"]`
- For `metadata` object: `{"key": "value", "number": 123}`

### Using Dropdowns:
- Foreign key fields (like `owner_id`, `property_id`) will show dropdowns
- Select the appropriate related record

### Data Types:
- **UUID**: Use the provided UUIDs or let Studio generate them
- **Numbers**: Enter without quotes (e.g., `500000`)
- **Text**: Enter with quotes (e.g., `"Downtown"`)
- **Arrays**: Use JSON format `["item1", "item2"]`
- **Objects**: Use JSON format `{"key": "value"}`

## ✅ Verification

After adding data, verify it works:

1. **Check your Next.js app**: http://localhost:3000
2. **Visit the marketplace page** to see properties
3. **Check the dashboard** for user data
4. **Run queries** to verify:
   ```bash
   npm run db:query "SELECT * FROM users"
   npm run db:query "SELECT * FROM properties"
   npm run db:query "SELECT * FROM investments"
   ```

## 🚀 Next Steps

Once you have sample data:

1. **Test your app features** with real data
2. **Develop new features** using the local environment
3. **Add more data** as needed for testing
4. **Switch to remote** when ready: `node scripts/switch-env.js remote`

## 🔧 Troubleshooting

**Foreign Key Errors:**
- Make sure parent records exist first (users before properties, properties before investments)
- Use the exact UUIDs provided

**JSON Format Errors:**
- Use double quotes for JSON: `{"key": "value"}`
- Arrays: `["item1", "item2"]`
- Numbers don't need quotes: `123`

**Can't Insert:**
- Check if you're using the correct data types
- Verify required fields are filled
- Make sure UUIDs are valid format
