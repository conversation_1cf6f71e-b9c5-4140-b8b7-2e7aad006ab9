'use client';

import { useState, useEffect } from 'react';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useChainId, useConnect, useDisconnect } from 'wagmi';

export default function DebugWalletPage() {
  const [mounted, setMounted] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>({});
  
  const { address, isConnected, isConnecting, isDisconnected } = useAccount();
  const chainId = useChainId();
  const { connect, connectors, error, isLoading, pendingConnector } = useConnect();
  const { disconnect } = useDisconnect();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info = {
        // Browser info
        userAgent: navigator.userAgent,
        isChromeDesktop: navigator.userAgent.includes('Chrome') && !navigator.userAgent.includes('Mobile'),
        
        // Ethereum provider info
        hasEthereum: !!window.ethereum,
        isMetaMask: window.ethereum?.isMetaMask,
        providers: window.ethereum?.providers?.map((p: any) => ({
          isMetaMask: p.isMetaMask,
          isCoinbaseWallet: p.isCoinbaseWallet,
          isTrust: p.isTrust,
        })) || [],
        
        // Wagmi state
        isConnected,
        isConnecting,
        isDisconnected,
        address,
        chainId,
        
        // Available connectors
        connectors: connectors.map(connector => ({
          id: connector.id,
          name: connector.name,
          type: connector.type,
          ready: connector.ready,
        })),
        
        // Connection error
        error: error?.message,
        isLoading,
        pendingConnector: pendingConnector?.name,
      };
      
      setDebugInfo(info);
      console.log('Wallet Debug Info:', info);
    }
  }, [isConnected, isConnecting, isDisconnected, address, chainId, connectors, error, isLoading, pendingConnector]);

  if (!mounted) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center text-white">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Wallet Connection Debug</h1>
        
        {/* RainbowKit Connect Button */}
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">RainbowKit Connect Button</h2>
          <ConnectButton />
        </div>

        {/* Manual Connector Testing */}
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">Manual Connector Testing</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {connectors.map((connector) => (
              <button
                key={connector.id}
                onClick={() => connect({ connector })}
                disabled={!connector.ready || isLoading}
                className={`p-3 rounded-lg text-white font-medium ${
                  connector.ready 
                    ? 'bg-blue-600 hover:bg-blue-700' 
                    : 'bg-gray-600 cursor-not-allowed'
                }`}
              >
                {connector.name}
                {isLoading && pendingConnector?.id === connector.id && ' (connecting)'}
                {!connector.ready && ' (not ready)'}
              </button>
            ))}
          </div>
        </div>

        {/* Connection Status */}
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">Connection Status</h2>
          <div className="space-y-2 text-white">
            <p><strong>Connected:</strong> {isConnected ? 'Yes' : 'No'}</p>
            <p><strong>Connecting:</strong> {isConnecting ? 'Yes' : 'No'}</p>
            <p><strong>Address:</strong> {address || 'None'}</p>
            <p><strong>Chain ID:</strong> {chainId || 'None'}</p>
            {error && <p className="text-red-400"><strong>Error:</strong> {error.message}</p>}
          </div>
          {isConnected && (
            <button
              onClick={() => disconnect()}
              className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
            >
              Disconnect
            </button>
          )}
        </div>

        {/* Debug Information */}
        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-white mb-4">Debug Information</h2>
          <pre className="text-sm text-gray-300 overflow-auto max-h-96 bg-gray-900 p-4 rounded">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>

        {/* MetaMask Specific Tests */}
        <div className="bg-gray-800 p-6 rounded-lg mt-6">
          <h2 className="text-xl font-semibold text-white mb-4">MetaMask Specific Tests</h2>
          <div className="space-y-4">
            <button
              onClick={async () => {
                if (window.ethereum) {
                  try {
                    const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                    console.log('Direct MetaMask connection:', accounts);
                    alert(`Connected to MetaMask: ${accounts[0]}`);
                  } catch (err) {
                    console.error('Direct MetaMask connection failed:', err);
                    alert(`MetaMask connection failed: ${err.message}`);
                  }
                } else {
                  alert('MetaMask not detected');
                }
              }}
              className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg mr-4"
            >
              Test Direct MetaMask Connection
            </button>
            
            <button
              onClick={() => {
                if (window.ethereum?.isMetaMask) {
                  window.ethereum.request({ method: 'wallet_requestPermissions', params: [{ eth_accounts: {} }] });
                }
              }}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg"
            >
              Request MetaMask Permissions
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
