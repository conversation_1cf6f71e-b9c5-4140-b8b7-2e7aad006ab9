'use client';
import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useUser } from '../context/UserContext';
import { useWallet } from '../context/WalletContext';
import { useWalletAuth } from '../context/WalletAuthContext';
import { useNavigation } from '../context/NavigationContext';
import { Menu, X, User as UserIcon, LogOut, ChevronDown } from 'lucide-react';
import WalletAuthButton from './WalletAuthButton';
import ThemeToggle from './ThemeToggle';
import OptimizedLink from './OptimizedLink';

export default function Navbar() {
  const { user, signOut } = useUser();
  const { isConnected } = useWallet(); // Use the centralized wallet context
  const { user: walletUser, signOut: walletSignOut, isAuthenticated } = useWalletAuth();
  const pathname = usePathname();
  const router = useRouter();
  const { preloadRoute } = useNavigation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  // Preload routes on hover
  const handleLinkHover = (href: string) => {
    preloadRoute(href);
  };

  // Define navigation items based on role
  const guestNavItems = [
    { href: '/', label: 'Home' },
    { href: '/marketplace', label: 'Marketplace' },
    { href: '/how-it-works', label: 'How It Works' },
    { href: '/about', label: 'About' },
    { href: '/resources', label: 'Resources' },
  ];

  const authenticatedNavItems = [
    { href: '/', label: 'Home' },
    { href: '/marketplace', label: 'Marketplace' },
    { href: `/dashboard/${user?.role || ''}`, label: 'Dashboard' },
  ];

  // Dynamic role-based links
  const roleBasedLinks = {
    owner: [
      { href: '/dashboard/owner/properties', label: 'My Properties' },
      { href: '/dashboard/owner/investments', label: 'Investments' },
    ],
    investor: [
      { href: '/dashboard/investor/opportunities', label: 'Opportunities' },
      { href: '/dashboard/investor/portfolio', label: 'My Portfolio' },
    ],
    admin: [
      { href: '/dashboard/admin/users', label: 'User Management' },
      { href: '/dashboard/admin/properties', label: 'Properties' },
    ],
  };

  const navItems = user ? authenticatedNavItems : guestNavItems;
  const roleLinks = user?.role ? roleBasedLinks[user.role] || [] : [];

  return (
    <nav className="bg-indigo-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and desktop navigation */}
          <div className="flex items-center">
            <OptimizedLink href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-black">BC</span>
              </div>
              <span className="text-xl font-bold">BrickChain</span>
            </OptimizedLink>

            <div className="hidden md:ml-10 md:flex md:space-x-6">
              {navItems.map((item) => (
                <OptimizedLink
                  key={item.href}
                  href={item.href}
                  className="px-3 py-2 text-sm font-medium transition-colors text-gray-300 hover:text-white"
                  activeClassName="text-cyan-400"
                  exactMatch={item.href === '/'}
                  onMouseEnter={() => handleLinkHover(item.href)}
                >
                  {item.label}
                </OptimizedLink>
              ))}

              {/* Settings link for authenticated users */}
              {user && (
                <OptimizedLink
                  href="/settings"
                  className="px-3 py-2 text-sm font-medium text-gray-300 hover:text-white"
                  activeClassName="text-cyan-400"
                  onMouseEnter={() => handleLinkHover('/settings')}
                >
                  Settings
                </OptimizedLink>
              )}

              {/* Theme toggle */}
              <div className="ml-2">
                <ThemeToggle />
              </div>
            </div>
          </div>

          {/* Wallet authentication section */}
          <div className="hidden md:flex md:items-center md:space-x-4">
            <WalletAuthButton showKycStatus={true} />
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-300 hover:text-white hover:bg-indigo-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <X className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      {mobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 bg-indigo-800">
            {navItems.map((item) => (
              <OptimizedLink
                key={item.href}
                href={item.href}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-indigo-700 hover:text-white"
                activeClassName="bg-indigo-700 text-white"
                exactMatch={item.href === '/'}
                onClick={() => setMobileMenuOpen(false)}
                onMouseEnter={() => handleLinkHover(item.href)}
              >
                {item.label}
              </OptimizedLink>
            ))}

            {/* Settings link for mobile */}
            {user && (
              <OptimizedLink
                href="/settings"
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-indigo-700 hover:text-white"
                activeClassName="bg-indigo-700 text-white"
                onClick={() => setMobileMenuOpen(false)}
                onMouseEnter={() => handleLinkHover('/settings')}
              >
                Settings
              </OptimizedLink>
            )}

            {/* Theme toggle for mobile */}
            <div className="px-3 py-2 mt-2">
              <div className="flex items-center justify-between">
                <span className="text-base font-medium text-gray-300">Theme</span>
                <ThemeToggle variant="button" />
              </div>
            </div>
          </div>

          {/* Mobile wallet authentication section */}
          <div className="pt-4 pb-3 border-t border-indigo-700 bg-indigo-800">
            <div className="px-4 py-3">
              <div className="flex justify-center">
                <WalletAuthButton variant="sidebar" showKycStatus={true} />
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}